import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import currencyController from "../controllers/currency.controller";

const currencyRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Exchange Rate Schema
const exchangeRateResponseSchema = z.object({
  from: z.string(),
  to: z.string(),
  rate: z.number(),
  timestamp: z.string(),
});

// Currency Conversion Schema
const currencyConversionResponseSchema = z.object({
  originalAmount: z.number(),
  originalCurrency: z.string(),
  convertedAmount: z.number(),
  convertedCurrency: z.string(),
  exchangeRate: z.number(),
  lastUpdated: z.string(),
});

// Supported Currencies Schema
const supportedCurrencySchema = z.object({
  code: z.string(),
  name: z.string(),
  symbol: z.string(),
});

// Product Price Conversion Schema
const productPriceConversionSchema = z.object({
  originalPrice: z.number(),
  originalCurrency: z.string(),
  convertedPrice: z.number(),
  convertedCurrency: z.string(),
  exchangeRate: z.number().optional(),
  formattedPrice: z.string(),
});

// Currency Format Schema
const currencyFormatSchema = z.object({
  amount: z.number(),
  currency: z.string(),
  locale: z.string(),
  formatted: z.string(),
  symbol: z.string(),
});

// Get Exchange Rate Route
const getExchangeRateRoute = createRoute({
  method: "get",
  path: "/exchange-rate",
  request: {
    query: z.object({
      from: z.string().min(3).max(3),
      to: z.string().min(3).max(3),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: exchangeRateResponseSchema,
          }),
        },
      },
      description: "Exchange rate retrieved successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Bad request",
    },
  },
  tags: ["Currency"],
});

// Convert Currency Route
const convertCurrencyRoute = createRoute({
  method: "get",
  path: "/convert",
  request: {
    query: z.object({
      amount: z.string(),
      from: z.string().min(3).max(3),
      to: z.string().min(3).max(3),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: currencyConversionResponseSchema,
          }),
        },
      },
      description: "Currency converted successfully",
    },
  },
  tags: ["Currency"],
});

// Get Supported Currencies Route
const getSupportedCurrenciesRoute = createRoute({
  method: "get",
  path: "/supported",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.array(supportedCurrencySchema),
          }),
        },
      },
      description: "Supported currencies retrieved successfully",
    },
  },
  tags: ["Currency"],
});

// Get Multiple Exchange Rates Route
const getMultipleExchangeRatesRoute = createRoute({
  method: "get",
  path: "/rates",
  request: {
    query: z.object({
      base: z.string().min(3).max(3),
      targets: z.string(), // Comma-separated currency codes
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              base: z.string(),
              rates: z.record(z.number()),
              timestamp: z.string(),
            }),
          }),
        },
      },
      description: "Exchange rates retrieved successfully",
    },
  },
  tags: ["Currency"],
});

// Convert Product Price Route
const convertProductPriceRoute = createRoute({
  method: "get",
  path: "/product-price",
  request: {
    query: z.object({
      priceUSD: z.string(),
      locale: z.string(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: productPriceConversionSchema,
          }),
        },
      },
      description: "Product price converted successfully",
    },
  },
  tags: ["Currency"],
});

// Format Currency Route
const formatCurrencyRoute = createRoute({
  method: "get",
  path: "/format",
  request: {
    query: z.object({
      amount: z.string(),
      currency: z.string().min(3).max(3),
      locale: z.string().optional(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: currencyFormatSchema,
          }),
        },
      },
      description: "Currency formatted successfully",
    },
  },
  tags: ["Currency"],
});

// Register routes
currencyRoutes.openapi(getExchangeRateRoute, currencyController.getExchangeRate);
currencyRoutes.openapi(convertCurrencyRoute, currencyController.convertCurrency);
currencyRoutes.openapi(getSupportedCurrenciesRoute, currencyController.getSupportedCurrencies);
currencyRoutes.openapi(getMultipleExchangeRatesRoute, currencyController.getMultipleExchangeRates);
currencyRoutes.openapi(convertProductPriceRoute, currencyController.convertProductPrice);
currencyRoutes.openapi(formatCurrencyRoute, currencyController.formatCurrency);

export { currencyRoutes };
