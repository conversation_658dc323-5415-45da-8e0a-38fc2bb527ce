import { Context } from "hono";
import { errorResponse, successResponse } from "../utils/response.util";
import currencyService from "../services/currency.service";

class CurrencyController {
  /**
   * Get exchange rate between two currencies
   */
  async getExchangeRate(c: Context) {
    try {
      const { from, to } = c.req.query();

      if (!from || !to) {
        return c.json(errorResponse("Both 'from' and 'to' currencies are required"), 400);
      }

      if (!currencyService.isValidCurrency(from) || !currencyService.isValidCurrency(to)) {
        return c.json(errorResponse("Invalid currency code"), 400);
      }

      const rate = await currencyService.getExchangeRate(from, to);

      return c.json(successResponse("Exchange rate retrieved", {
        from,
        to,
        rate,
        timestamp: new Date().toISOString(),
      }));
    } catch (error: any) {
      console.error("Get exchange rate error:", error);
      return c.json(errorResponse(error.message || "Failed to get exchange rate"), 500);
    }
  }

  /**
   * Convert currency amount
   */
  async convertCurrency(c: Context) {
    try {
      const { amount, from, to } = c.req.query();

      if (!amount || !from || !to) {
        return c.json(errorResponse("Amount, from, and to currencies are required"), 400);
      }

      const numAmount = parseFloat(amount);
      if (isNaN(numAmount) || numAmount <= 0) {
        return c.json(errorResponse("Invalid amount"), 400);
      }

      if (!currencyService.isValidCurrency(from) || !currencyService.isValidCurrency(to)) {
        return c.json(errorResponse("Invalid currency code"), 400);
      }

      const result = await currencyService.convertCurrency(numAmount, from, to);

      return c.json(successResponse("Currency converted", result));
    } catch (error: any) {
      console.error("Convert currency error:", error);
      return c.json(errorResponse(error.message || "Failed to convert currency"), 500);
    }
  }

  /**
   * Get supported currencies
   */
  async getSupportedCurrencies(c: Context) {
    try {
      const currencies = currencyService.getSupportedCurrencies();

      return c.json(successResponse("Supported currencies retrieved", currencies));
    } catch (error: any) {
      console.error("Get supported currencies error:", error);
      return c.json(errorResponse(error.message || "Failed to get supported currencies"), 500);
    }
  }

  /**
   * Get multiple exchange rates
   */
  async getMultipleExchangeRates(c: Context) {
    try {
      const { base, targets } = c.req.query();

      if (!base || !targets) {
        return c.json(errorResponse("Base currency and target currencies are required"), 400);
      }

      if (!currencyService.isValidCurrency(base)) {
        return c.json(errorResponse("Invalid base currency"), 400);
      }

      const targetCurrencies = targets.split(',').map(t => t.trim());
      
      for (const target of targetCurrencies) {
        if (!currencyService.isValidCurrency(target)) {
          return c.json(errorResponse(`Invalid target currency: ${target}`), 400);
        }
      }

      const rates = await currencyService.getMultipleExchangeRates(base, targetCurrencies);

      return c.json(successResponse("Exchange rates retrieved", {
        base,
        rates,
        timestamp: new Date().toISOString(),
      }));
    } catch (error: any) {
      console.error("Get multiple exchange rates error:", error);
      return c.json(errorResponse(error.message || "Failed to get exchange rates"), 500);
    }
  }

  /**
   * Convert product price based on locale
   */
  async convertProductPrice(c: Context) {
    try {
      const { priceUSD, locale } = c.req.query();

      if (!priceUSD || !locale) {
        return c.json(errorResponse("Price USD and locale are required"), 400);
      }

      const numPrice = parseFloat(priceUSD);
      if (isNaN(numPrice) || numPrice <= 0) {
        return c.json(errorResponse("Invalid price"), 400);
      }

      const result = await currencyService.convertProductPrice(numPrice, locale);

      return c.json(successResponse("Product price converted", {
        originalPrice: numPrice,
        originalCurrency: 'USD',
        convertedPrice: result.amount,
        convertedCurrency: result.currency,
        exchangeRate: result.exchangeRate,
        formattedPrice: currencyService.formatCurrency(result.amount, result.currency, locale),
      }));
    } catch (error: any) {
      console.error("Convert product price error:", error);
      return c.json(errorResponse(error.message || "Failed to convert product price"), 500);
    }
  }

  /**
   * Format currency amount
   */
  async formatCurrency(c: Context) {
    try {
      const { amount, currency, locale } = c.req.query();

      if (!amount || !currency) {
        return c.json(errorResponse("Amount and currency are required"), 400);
      }

      const numAmount = parseFloat(amount);
      if (isNaN(numAmount)) {
        return c.json(errorResponse("Invalid amount"), 400);
      }

      if (!currencyService.isValidCurrency(currency)) {
        return c.json(errorResponse("Invalid currency code"), 400);
      }

      const formatted = currencyService.formatCurrency(numAmount, currency, locale);

      return c.json(successResponse("Currency formatted", {
        amount: numAmount,
        currency,
        locale: locale || 'en-US',
        formatted,
        symbol: currencyService.getCurrencySymbol(currency),
      }));
    } catch (error: any) {
      console.error("Format currency error:", error);
      return c.json(errorResponse(error.message || "Failed to format currency"), 500);
    }
  }
}

export default new CurrencyController();
