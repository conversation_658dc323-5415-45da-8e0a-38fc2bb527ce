import { Context } from "hono";
import { errorResponse, successResponse } from "../utils/response.util";
import paymentService from "../services/payment.service";
import { prisma } from "../db";
import { CreateInvoiceInput, CreateEWalletChargeInput } from "../schemas/payment.schema";

class PaymentController {
  /**
   * Create Xendit invoice for payment
   */
  async createInvoice(c: Context) {
    try {
      const body = await c.req.json() as CreateInvoiceInput;
      
      // Verify order exists and belongs to user
      const userId = c.get("jwtPayload")?.sub;
      const order = await prisma.order.findFirst({
        where: {
          id: body.orderId,
          userId: userId,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findUnique({
        where: { orderId: body.orderId },
      });

      if (existingPayment && existingPayment.status === 'PAID') {
        return c.json(errorResponse("Order already paid"), 400);
      }

      // Create invoice
      const invoice = await paymentService.createInvoice(body);

      return c.json(successResponse("Invoice created successfully", invoice));
    } catch (error: any) {
      console.error("Create invoice error:", error);
      return c.json(errorResponse(error.message || "Failed to create invoice"), 500);
    }
  }

  /**
   * Create eWallet charge
   */
  async createEWalletCharge(c: Context) {
    try {
      const body = await c.req.json() as CreateEWalletChargeInput;
      
      // Verify order exists and belongs to user
      const userId = c.get("jwtPayload")?.sub;
      const order = await prisma.order.findFirst({
        where: {
          id: body.orderId,
          userId: userId,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      // Create eWallet charge
      const charge = await paymentService.createEWalletCharge(body);

      return c.json(successResponse("eWallet charge created successfully", charge));
    } catch (error: any) {
      console.error("Create eWallet charge error:", error);
      return c.json(errorResponse(error.message || "Failed to create eWallet charge"), 500);
    }
  }

  /**
   * Get invoice status
   */
  async getInvoiceStatus(c: Context) {
    try {
      const { invoiceId } = c.req.param();
      
      if (!invoiceId) {
        return c.json(errorResponse("Invoice ID is required"), 400);
      }

      const invoice = await paymentService.getInvoiceStatus(invoiceId);

      return c.json(successResponse("Invoice status retrieved", invoice));
    } catch (error: any) {
      console.error("Get invoice status error:", error);
      return c.json(errorResponse(error.message || "Failed to get invoice status"), 500);
    }
  }

  /**
   * Get payment status by order ID
   */
  async getPaymentStatus(c: Context) {
    try {
      const { orderId } = c.req.param();
      const userId = c.get("jwtPayload")?.sub;

      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      // Verify order belongs to user
      const order = await prisma.order.findFirst({
        where: {
          id: orderId,
          userId: userId,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      const payment = await prisma.payment.findUnique({
        where: { orderId },
      });

      if (!payment) {
        return c.json(errorResponse("Payment not found"), 404);
      }

      return c.json(successResponse("Payment status retrieved", {
        id: payment.id,
        orderId: payment.orderId,
        status: payment.status,
        amount: payment.amount,
        currency: payment.currency,
        paidAt: payment.paidAt,
        paymentMethod: payment.paymentMethod,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      }));
    } catch (error: any) {
      console.error("Get payment status error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment status"), 500);
    }
  }

  /**
   * Handle Xendit webhook
   */
  async handleWebhook(c: Context) {
    try {
      const signature = c.req.header('x-callback-token') || '';
      const payload = await c.req.json();

      console.log('Webhook received:', { payload, signature });

      const result = await paymentService.handleWebhook(payload, signature);

      return c.json(successResponse("Webhook processed successfully", result));
    } catch (error: any) {
      console.error("Webhook handling error:", error);
      return c.json(errorResponse(error.message || "Failed to process webhook"), 500);
    }
  }

  /**
   * Get supported payment methods
   */
  async getPaymentMethods(c: Context) {
    try {
      const { currency } = c.req.query();

      if (!currency || !['IDR', 'USD'].includes(currency)) {
        return c.json(errorResponse("Valid currency (IDR or USD) is required"), 400);
      }

      const methods = paymentService.getPaymentMethods(currency as 'IDR' | 'USD');

      return c.json(successResponse("Payment methods retrieved", methods));
    } catch (error: any) {
      console.error("Get payment methods error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment methods"), 500);
    }
  }

  /**
   * Get user's payment history
   */
  async getPaymentHistory(c: Context) {
    try {
      const userId = c.get("jwtPayload")?.sub;
      const { page = "1", limit = "10", status } = c.req.query();

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      const where: any = {
        order: {
          userId: userId,
        },
      };

      if (status) {
        where.status = status;
      }

      const [payments, total] = await Promise.all([
        prisma.payment.findMany({
          where,
          include: {
            order: {
              select: {
                orderNumber: true,
                total: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limitNum,
        }),
        prisma.payment.count({ where }),
      ]);

      return c.json(successResponse("Payment history retrieved", {
        payments,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      }));
    } catch (error: any) {
      console.error("Get payment history error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment history"), 500);
    }
  }
}

export default new PaymentController();
