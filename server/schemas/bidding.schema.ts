import { z } from "zod";

// Bid schema
export const bidSchema = z.object({
  productId: z.string().uuid("Invalid product ID"),
  amount: z.number().min(0.01, "Bid amount must be greater than 0"),
});

// Auto-bid schema
export const autoBidSchema = z.object({
  productId: z.string().uuid("Invalid product ID"),
  maxBudget: z.number().min(0.01, "Max budget must be greater than 0"),
  incrementStep: z.number().min(0.01, "Increment step must be greater than 0"),
});

// Manual bid schema (extended)
export const manualBidSchema = z.object({
  productId: z.string().uuid("Invalid product ID"),
  amount: z.number().min(0.01, "Bid amount must be greater than 0"),
  bidType: z.enum(["manual", "auto"]).default("manual"),
});

// Auto-bid settings schema
export const autoBidSettingsSchema = z.object({
  id: z.string().uuid(),
  productId: z.string().uuid(),
  userId: z.string().uuid(),
  maxBudget: z.number(),
  incrementStep: z.number(),
  isActive: z.boolean(),
  currentBid: z.number().nullable(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Product in bid schema
export const productInBidSchema = z.object({
  id: z.string().uuid(),
  itemName: z.string(),
  slug: z.string().nullable(),
  priceUSD: z.number(),
  currentBid: z.number().nullable(),
  bidCount: z.number().int(),
  auctionStartDate: z.string().datetime().nullable(),
  auctionEndDate: z.string().datetime().nullable(),
  status: z.string(),
  sellType: z.enum(["auction", "buy-now"]),
  images: z.array(z.object({
    id: z.string().uuid(),
    imageUrl: z.string(),
    altText: z.string().nullable(),
    sortOrder: z.number().int(),
    isMain: z.boolean(),
  })),
  seller: z.object({
    id: z.string().uuid(),
    name: z.string(),
    email: z.string(),
  }),
});

// Bidder schema
export const bidderSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  email: z.string(),
});

// Bid response schema
export const bidResponseSchema = z.object({
  id: z.string().uuid(),
  productId: z.string().uuid(),
  bidderId: z.string().uuid(),
  amount: z.number(),
  isWinning: z.boolean(),
  createdAt: z.string().datetime(),
  bidder: bidderSchema,
  product: productInBidSchema,
});

// Bid history schema
export const bidHistorySchema = z.object({
  productId: z.string().uuid(),
  bids: z.array(z.object({
    id: z.string().uuid(),
    amount: z.number(),
    isWinning: z.boolean(),
    createdAt: z.string().datetime(),
    bidder: z.object({
      id: z.string().uuid(),
      name: z.string(),
    }),
  })),
  totalBids: z.number().int(),
  highestBid: z.number().nullable(),
  currentWinner: bidderSchema.nullable(),
});

// User bid summary schema
export const userBidSummarySchema = z.object({
  productId: z.string().uuid(),
  product: z.object({
    id: z.string().uuid(),
    itemName: z.string(),
    slug: z.string().nullable(),
    currentBid: z.number().nullable(),
    auctionEndDate: z.string().datetime().nullable(),
    status: z.string(),
    images: z.array(z.object({
      id: z.string().uuid(),
      imageUrl: z.string(),
      isMain: z.boolean(),
    })),
  }),
  highestBid: z.number(),
  totalBids: z.number().int(),
  isWinning: z.boolean(),
  lastBidTime: z.string().datetime(),
  auctionStatus: z.enum(["active", "ended", "won", "lost"]),
});

// User bids list response schema
export const userBidsListResponseSchema = z.object({
  bids: z.array(userBidSummarySchema),
  pagination: z.object({
    page: z.number().int(),
    limit: z.number().int(),
    total: z.number().int(),
    totalPages: z.number().int(),
  }),
});

// Query schema for user bids
export const userBidsQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().int().min(1)).default("1"),
  limit: z.string().transform(Number).pipe(z.number().int().min(1).max(100)).default("10"),
  status: z.enum(["active", "ended", "won", "lost"]).optional(),
  sortBy: z.enum(["createdAt", "amount", "auctionEndDate"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Export types
export type BidData = z.infer<typeof bidSchema>;
export type AutoBidData = z.infer<typeof autoBidSchema>;
export type ManualBidData = z.infer<typeof manualBidSchema>;
export type AutoBidSettings = z.infer<typeof autoBidSettingsSchema>;
export type ProductInBid = z.infer<typeof productInBidSchema>;
export type Bidder = z.infer<typeof bidderSchema>;
export type BidResponse = z.infer<typeof bidResponseSchema>;
export type BidHistory = z.infer<typeof bidHistorySchema>;
export type UserBidSummary = z.infer<typeof userBidSummarySchema>;
export type UserBidsListResponse = z.infer<typeof userBidsListResponseSchema>;
export type UserBidsQuery = z.infer<typeof userBidsQuerySchema>;
