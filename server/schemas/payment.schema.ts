import { z } from "zod";

// Create Invoice Schema
export const createInvoiceSchema = z.object({
  orderId: z.string().min(1, "Order ID is required"),
  amount: z.number().positive("Amount must be positive"),
  currency: z.enum(["IDR", "USD"], {
    errorMap: () => ({ message: "Currency must be IDR or USD" }),
  }),
  customerEmail: z.string().email("Invalid email format"),
  customerName: z.string().min(1, "Customer name is required"),
  description: z.string().min(1, "Description is required"),
  successRedirectUrl: z.string().url().optional(),
  failureRedirectUrl: z.string().url().optional(),
});

// Create eWallet Charge Schema
export const createEWalletChargeSchema = z.object({
  orderId: z.string().min(1, "Order ID is required"),
  amount: z.number().positive("Amount must be positive"),
  currency: z.enum(["IDR", "USD"]),
  ewalletType: z.enum(["OVO", "DANA", "LINKAJA", "SHOPEEPAY"]),
  customerPhone: z.string().min(10, "Valid phone number is required"),
  customerName: z.string().min(1, "Customer name is required"),
});

// Webhook Payload Schema
export const webhookPayloadSchema = z.object({
  id: z.string(),
  external_id: z.string(),
  user_id: z.string(),
  status: z.string(),
  merchant_name: z.string(),
  amount: z.number(),
  paid_amount: z.number().optional(),
  bank_code: z.string().optional(),
  paid_at: z.string().optional(),
  payer_email: z.string().optional(),
  description: z.string().optional(),
  adjusted_received_amount: z.number().optional(),
  fees_paid_amount: z.number().optional(),
  updated: z.string(),
  created: z.string(),
  currency: z.string(),
  payment_method: z.string().optional(),
  payment_channel: z.string().optional(),
  payment_destination: z.string().optional(),
});

// Invoice Response Schema
export const invoiceResponseSchema = z.object({
  id: z.string(),
  externalId: z.string(),
  status: z.string(),
  amount: z.number(),
  currency: z.string(),
  invoiceUrl: z.string(),
  expiryDate: z.string(),
});

// Payment Status Response Schema
export const paymentStatusResponseSchema = z.object({
  id: z.string(),
  orderId: z.string(),
  status: z.string(),
  amount: z.number(),
  currency: z.string(),
  paidAt: z.string().nullable(),
  paymentMethod: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Payment Methods Response Schema
export const paymentMethodsResponseSchema = z.object({
  invoice: z.boolean(),
  ewallet: z.array(z.string()),
  virtualAccount: z.array(z.string()),
  retailOutlet: z.array(z.string()),
});

// Query Schemas
export const getInvoiceStatusSchema = z.object({
  invoiceId: z.string().min(1, "Invoice ID is required"),
});

export const getPaymentMethodsSchema = z.object({
  currency: z.enum(["IDR", "USD"]),
});

// Type exports
export type CreateInvoiceInput = z.infer<typeof createInvoiceSchema>;
export type CreateEWalletChargeInput = z.infer<typeof createEWalletChargeSchema>;
export type WebhookPayload = z.infer<typeof webhookPayloadSchema>;
export type InvoiceResponse = z.infer<typeof invoiceResponseSchema>;
export type PaymentStatusResponse = z.infer<typeof paymentStatusResponseSchema>;
export type PaymentMethodsResponse = z.infer<typeof paymentMethodsResponseSchema>;
