import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import { UserBidsQuery, AutoBidData, ManualBidData } from "../schemas/bidding.schema";

class BiddingService {
  // Get user's bidding history with pagination and filtering
  async getUserBids(userId: string, query: UserBidsQuery) {
    try {
      const { page, limit, status, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      // Build where clause for bids
      const where: any = { bidderId: userId };

      // Get user's bids grouped by product
      const userBids = await prisma.bid.findMany({
        where,
        include: {
          product: {
            include: {
              images: {
                where: { isMain: true },
                orderBy: { sortOrder: 'asc' }
              },
              bids: {
                orderBy: { amount: 'desc' },
                take: 1,
                include: {
                  bidder: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      // Group bids by product and get summary
      const bidSummaryMap = new Map();
      
      for (const bid of userBids) {
        const productId = bid.productId;
        
        if (!bidSummaryMap.has(productId)) {
          // Get all bids for this product by this user
          const userProductBids = await prisma.bid.findMany({
            where: {
              productId,
              bidderId: userId
            },
            orderBy: { amount: 'desc' }
          });

          // Get highest bid for this product (from any user)
          const highestBid = await prisma.bid.findFirst({
            where: { productId },
            orderBy: { amount: 'desc' },
            include: { bidder: true }
          });

          // Determine auction status
          const now = new Date();
          const auctionEndDate = bid.product.auctionEndDate;
          let auctionStatus: "active" | "ended" | "won" | "lost" = "active";
          
          if (auctionEndDate && now > auctionEndDate) {
            auctionStatus = "ended";
            if (highestBid && highestBid.bidderId === userId) {
              auctionStatus = "won";
            } else {
              auctionStatus = "lost";
            }
          }

          // Filter by status if provided
          if (status && auctionStatus !== status) {
            continue;
          }

          const userHighestBid = userProductBids[0];
          const isWinning = highestBid?.bidderId === userId;

          bidSummaryMap.set(productId, {
            productId,
            product: {
              id: bid.product.id,
              itemName: bid.product.itemName,
              slug: bid.product.slug,
              currentBid: bid.product.currentBid ? Number(bid.product.currentBid) : null,
              auctionEndDate: bid.product.auctionEndDate?.toISOString() || null,
              status: bid.product.status,
              images: bid.product.images.map(img => ({
                id: img.id,
                imageUrl: img.imageUrl,
                isMain: img.isMain,
              })),
            },
            highestBid: Number(userHighestBid.amount),
            totalBids: userProductBids.length,
            isWinning,
            lastBidTime: userHighestBid.createdAt.toISOString(),
            auctionStatus,
          });
        }
      }

      // Convert map to array and apply sorting
      let bidSummaries = Array.from(bidSummaryMap.values());

      // Sort results
      bidSummaries.sort((a, b) => {
        let aValue, bValue;
        
        switch (sortBy) {
          case 'amount':
            aValue = a.highestBid;
            bValue = b.highestBid;
            break;
          case 'auctionEndDate':
            aValue = new Date(a.product.auctionEndDate || 0).getTime();
            bValue = new Date(b.product.auctionEndDate || 0).getTime();
            break;
          default: // createdAt
            aValue = new Date(a.lastBidTime).getTime();
            bValue = new Date(b.lastBidTime).getTime();
        }

        if (sortOrder === 'desc') {
          return bValue - aValue;
        }
        return aValue - bValue;
      });

      // Apply pagination
      const total = bidSummaries.length;
      const paginatedBids = bidSummaries.slice(skip, skip + limit);
      const totalPages = Math.ceil(total / limit);

      return successResponse("User bids retrieved successfully", {
        bids: paginatedBids,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        }
      });
    } catch (error) {
      console.error("Get user bids error:", error);
      return errorResponse("Failed to retrieve user bids");
    }
  }

  // Get single bid detail
  async getBidDetail(userId: string, bidId: string) {
    try {
      const bid = await prisma.bid.findFirst({
        where: {
          id: bidId,
          bidderId: userId, // Ensure user can only access their own bids
        },
        include: {
          product: {
            include: {
              images: {
                orderBy: { sortOrder: 'asc' }
              },
              seller: {
                select: {
                  id: true,
                  email: true,
                }
              },
              bids: {
                orderBy: { amount: 'desc' },
                include: {
                  bidder: {
                    select: {
                      id: true,
                      email: true,
                    }
                  }
                }
              }
            }
          },
          bidder: {
            select: {
              id: true,
              email: true,
            }
          }
        }
      });

      if (!bid) {
        return errorResponse("Bid not found");
      }

      // Transform data to match schema
      const transformedBid = {
        ...bid,
        amount: Number(bid.amount),
        createdAt: bid.createdAt.toISOString(),
        product: {
          ...bid.product,
          priceUSD: Number(bid.product.priceUSD),
          currentBid: bid.product.currentBid ? Number(bid.product.currentBid) : null,
          auctionStartDate: bid.product.auctionStartDate?.toISOString() || null,
          auctionEndDate: bid.product.auctionEndDate?.toISOString() || null,
          images: bid.product.images.map(img => ({
            ...img,
            altText: img.altText || null,
          }))
        }
      };

      return successResponse("Bid detail retrieved successfully", transformedBid);
    } catch (error) {
      console.error("Get bid detail error:", error);
      return errorResponse("Failed to retrieve bid detail");
    }
  }

  // Get bid history for a product
  async getBidHistory(productId: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      const bids = await prisma.bid.findMany({
        where: { productId },
        include: {
          bidder: {
            select: {
              id: true,
              email: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      const highestBid = bids.length > 0 ? Math.max(...bids.map(b => Number(b.amount))) : null;
      const currentWinner = bids.find(b => Number(b.amount) === highestBid)?.bidder || null;

      const transformedBids = bids.map(bid => ({
        ...bid,
        amount: Number(bid.amount),
        createdAt: bid.createdAt.toISOString(),
      }));

      return successResponse("Bid history retrieved successfully", {
        productId,
        bids: transformedBids,
        totalBids: bids.length,
        highestBid,
        currentWinner,
      });
    } catch (error) {
      console.error("Get bid history error:", error);
      return errorResponse("Failed to retrieve bid history");
    }
  }

  // Setup auto-bid for a product
  async setupAutoBid(productId: string, userId: string, data: AutoBidData) {
    try {
      const { maxBudget, incrementStep } = data;

      // Validate product
      const product = await prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      if (product.sellType !== 'auction') {
        return errorResponse("Auto-bid is only available for auction items");
      }

      if (product.sellerId === userId) {
        return errorResponse("You cannot set auto-bid on your own product");
      }

      // Check if auction has ended
      if (product.auctionEndDate && new Date() > product.auctionEndDate) {
        return errorResponse("Auction has ended");
      }

      // Validate budget
      const currentBid = Number(product.currentBid || product.priceUSD);
      if (maxBudget <= currentBid) {
        return errorResponse(`Max budget must be higher than current bid ($${currentBid})`);
      }

      // Create or update auto-bid
      const autoBid = await prisma.autoBid.upsert({
        where: {
          productId_userId: {
            productId,
            userId,
          },
        },
        update: {
          maxBudget,
          incrementStep,
          isActive: true,
        },
        create: {
          productId,
          userId,
          maxBudget,
          incrementStep,
          isActive: true,
        },
      });

      return successResponse("Auto-bid setup successfully", autoBid);
    } catch (error) {
      console.error("Setup auto-bid error:", error);
      return errorResponse("Failed to setup auto-bid");
    }
  }

  // Get user's auto-bid settings for a product
  async getUserAutoBid(productId: string, userId: string) {
    try {
      const autoBid = await prisma.autoBid.findUnique({
        where: {
          productId_userId: {
            productId,
            userId,
          },
        },
      });

      return successResponse("Auto-bid settings retrieved", autoBid);
    } catch (error) {
      console.error("Get auto-bid error:", error);
      return errorResponse("Failed to get auto-bid settings");
    }
  }

  // Deactivate auto-bid
  async deactivateAutoBid(productId: string, userId: string) {
    try {
      const autoBid = await prisma.autoBid.update({
        where: {
          productId_userId: {
            productId,
            userId,
          },
        },
        data: { isActive: false },
      });

      return successResponse("Auto-bid deactivated", autoBid);
    } catch (error) {
      console.error("Deactivate auto-bid error:", error);
      return errorResponse("Failed to deactivate auto-bid");
    }
  }

  // Process auto-bids when a new bid is placed
  async processAutoBids(productId: string, newBidAmount: number, excludeUserId: string) {
    try {
      // Get all active auto-bids for this product (excluding the user who just bid)
      const autoBids = await prisma.autoBid.findMany({
        where: {
          productId,
          isActive: true,
          userId: { not: excludeUserId },
          maxBudget: { gt: newBidAmount },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { maxBudget: 'desc' }, // Process highest budget first
      });

      for (const autoBid of autoBids) {
        const nextBidAmount = newBidAmount + Number(autoBid.incrementStep);

        // Check if auto-bid budget allows this bid
        if (nextBidAmount <= Number(autoBid.maxBudget)) {
          try {
            // Place auto-bid using existing placeBid method
            await this.placeBidInternal(productId, autoBid.userId, nextBidAmount);

            // Update auto-bid current bid
            await prisma.autoBid.update({
              where: { id: autoBid.id },
              data: { currentBid: nextBidAmount },
            });

            // Update newBidAmount for next iteration
            newBidAmount = nextBidAmount;
          } catch (error) {
            console.error('Auto-bid failed:', error);
            // Deactivate auto-bid if it fails
            await prisma.autoBid.update({
              where: { id: autoBid.id },
              data: { isActive: false },
            });
          }
        } else {
          // Deactivate auto-bid if budget exceeded
          await prisma.autoBid.update({
            where: { id: autoBid.id },
            data: { isActive: false },
          });
        }
      }
    } catch (error) {
      console.error("Process auto-bids error:", error);
    }
  }

  // Internal method to place bid (used by both manual and auto-bid)
  private async placeBidInternal(productId: string, userId: string, amount: number) {
    // This is a simplified version - you should implement the full bid validation logic here
    const bid = await prisma.bid.create({
      data: {
        productId,
        bidderId: userId,
        amount,
        isWinning: true,
      },
    });

    // Update previous winning bids to not winning
    await prisma.bid.updateMany({
      where: {
        productId,
        id: { not: bid.id },
        isWinning: true,
      },
      data: { isWinning: false },
    });

    // Update product with new current bid and bid count
    await prisma.product.update({
      where: { id: productId },
      data: {
        currentBid: amount,
        bidCount: { increment: 1 },
      },
    });

    return bid;
  }
}

export default new BiddingService();
