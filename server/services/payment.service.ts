import { Xendit } from 'xendit-node';
import { prisma } from '../db';
import { errorResponse, successResponse } from '../utils/response.util';

// Initialize Xendit client
const xenditClient = new Xendit({
  secretKey: process.env.XENDIT_SECRET_KEY!,
});

export interface CreateInvoiceInput {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  customerEmail: string;
  customerName: string;
  description: string;
  successRedirectUrl?: string;
  failureRedirectUrl?: string;
}

export interface InvoiceResponse {
  id: string;
  externalId: string;
  status: string;
  amount: number;
  currency: string;
  invoiceUrl: string;
  expiryDate: string;
}

class PaymentService {
  /**
   * Create Xendit invoice for payment
   */
  async createInvoice(data: CreateInvoiceInput): Promise<InvoiceResponse> {
    try {
      // Validate currency and amount
      if (data.currency === 'IDR' && data.amount < 10000) {
        throw new Error('Minimum amount for IDR is 10,000');
      }
      if (data.currency === 'USD' && data.amount < 1) {
        throw new Error('Minimum amount for USD is $1');
      }

      const invoice = await xenditClient.Invoice.createInvoice({
        data: {
          externalId: data.orderId,
          amount: data.amount,
          currency: data.currency,
          customer: {
            email: data.customerEmail,
            givenNames: data.customerName,
          },
          description: data.description,
          invoiceDuration: 86400, // 24 hours
          successRedirectUrl: data.successRedirectUrl || `${process.env.NEXTAUTH_URL}/orders/${data.orderId}?status=success`,
          failureRedirectUrl: data.failureRedirectUrl || `${process.env.NEXTAUTH_URL}/orders/${data.orderId}?status=failed`,
          reminderTime: 1, // Send reminder 1 hour before expiry
          locale: data.currency === 'IDR' ? 'id' : 'en',
        },
      });

      // Save invoice to database
      await prisma.payment.create({
        data: {
          id: invoice.id,
          orderId: data.orderId,
          externalId: data.orderId,
          amount: data.amount,
          currency: data.currency,
          status: invoice.status,
          invoiceUrl: invoice.invoiceUrl,
          expiryDate: new Date(invoice.expiryDate),
          xenditInvoiceId: invoice.id,
        },
      });

      return {
        id: invoice.id,
        externalId: invoice.externalId,
        status: invoice.status,
        amount: invoice.amount,
        currency: invoice.currency,
        invoiceUrl: invoice.invoiceUrl,
        expiryDate: invoice.expiryDate,
      };
    } catch (error: any) {
      console.error('Create invoice error:', error);
      throw new Error(`Failed to create invoice: ${error.message}`);
    }
  }

  /**
   * Get invoice status from Xendit
   */
  async getInvoiceStatus(invoiceId: string) {
    try {
      const invoice = await xenditClient.Invoice.getInvoice({
        invoiceId,
      });

      // Update status in database
      await prisma.payment.update({
        where: { xenditInvoiceId: invoiceId },
        data: {
          status: invoice.status,
          paidAt: invoice.status === 'PAID' ? new Date() : null,
        },
      });

      return invoice;
    } catch (error: any) {
      console.error('Get invoice status error:', error);
      throw new Error(`Failed to get invoice status: ${error.message}`);
    }
  }

  /**
   * Handle webhook callback from Xendit
   */
  async handleWebhook(payload: any, signature: string) {
    try {
      // Verify webhook signature
      const isValid = this.verifyWebhookSignature(payload, signature);
      if (!isValid) {
        throw new Error('Invalid webhook signature');
      }

      const { id, external_id, status, paid_amount, currency } = payload;

      // Update payment status in database
      const payment = await prisma.payment.update({
        where: { xenditInvoiceId: id },
        data: {
          status: status,
          paidAt: status === 'PAID' ? new Date() : null,
          paidAmount: paid_amount || null,
        },
      });

      // Update order status based on payment status
      if (status === 'PAID') {
        await prisma.order.update({
          where: { id: external_id },
          data: {
            paymentStatus: 'paid',
            status: 'processing',
          },
        });
      } else if (status === 'EXPIRED' || status === 'FAILED') {
        await prisma.order.update({
          where: { id: external_id },
          data: {
            paymentStatus: 'failed',
            status: 'cancelled',
          },
        });
      }

      return payment;
    } catch (error: any) {
      console.error('Webhook handling error:', error);
      throw new Error(`Failed to handle webhook: ${error.message}`);
    }
  }

  /**
   * Verify webhook signature from Xendit
   */
  private verifyWebhookSignature(payload: any, signature: string): boolean {
    try {
      const crypto = require('crypto');
      const webhookToken = process.env.XENDIT_WEBHOOK_TOKEN!;
      
      const computedSignature = crypto
        .createHmac('sha256', webhookToken)
        .update(JSON.stringify(payload))
        .digest('hex');

      return computedSignature === signature;
    } catch (error) {
      console.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Create eWallet charge (alternative payment method)
   */
  async createEWalletCharge(data: {
    orderId: string;
    amount: number;
    currency: 'IDR' | 'USD';
    ewalletType: 'OVO' | 'DANA' | 'LINKAJA' | 'SHOPEEPAY';
    customerPhone: string;
    customerName: string;
  }) {
    try {
      const charge = await xenditClient.EWallet.createEWalletCharge({
        data: {
          referenceId: data.orderId,
          currency: data.currency,
          amount: data.amount,
          checkoutMethod: 'ONE_TIME_PAYMENT',
          channelCode: data.ewalletType,
          channelProperties: {
            successRedirectUrl: `${process.env.NEXTAUTH_URL}/orders/${data.orderId}?status=success`,
            failureRedirectUrl: `${process.env.NEXTAUTH_URL}/orders/${data.orderId}?status=failed`,
            mobileNumber: data.customerPhone,
          },
          customer: {
            referenceId: data.orderId,
            type: 'INDIVIDUAL',
            individualDetail: {
              givenNames: data.customerName,
            },
          },
        },
      });

      return charge;
    } catch (error: any) {
      console.error('Create eWallet charge error:', error);
      throw new Error(`Failed to create eWallet charge: ${error.message}`);
    }
  }

  /**
   * Get supported payment methods based on currency
   */
  getPaymentMethods(currency: 'IDR' | 'USD') {
    if (currency === 'IDR') {
      return {
        invoice: true,
        ewallet: ['OVO', 'DANA', 'LINKAJA', 'SHOPEEPAY'],
        virtualAccount: ['BCA', 'BNI', 'BRI', 'MANDIRI'],
        retailOutlet: ['ALFAMART', 'INDOMARET'],
      };
    } else {
      return {
        invoice: true,
        ewallet: [],
        virtualAccount: [],
        retailOutlet: [],
      };
    }
  }
}

export default new PaymentService();
