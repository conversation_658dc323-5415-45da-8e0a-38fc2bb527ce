import { prisma } from '../db';

interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  lastUpdated: Date;
}

interface CurrencyConversionResult {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  lastUpdated: Date;
}

class CurrencyService {
  private readonly EXCHANGE_API_URL = 'https://api.exchangerate-api.com/v4/latest';
  private readonly FALLBACK_RATES = {
    'USD_TO_IDR': 15800,
    'IDR_TO_USD': 1 / 15800,
  };

  /**
   * Get current exchange rate between two currencies
   */
  async getExchangeRate(from: string, to: string): Promise<number> {
    try {
      // Check if we have a recent rate in database (less than 1 hour old)
      const cachedRate = await prisma.exchangeRate.findFirst({
        where: {
          fromCurrency: from,
          toCurrency: to,
          updatedAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
          },
        },
        orderBy: { updatedAt: 'desc' },
      });

      if (cachedRate) {
        return cachedRate.rate;
      }

      // Fetch from external API
      const rate = await this.fetchExchangeRateFromAPI(from, to);
      
      // Save to database
      await this.saveExchangeRate(from, to, rate);
      
      return rate;
    } catch (error) {
      console.error('Error getting exchange rate:', error);
      
      // Return fallback rate
      const fallbackKey = `${from}_TO_${to}` as keyof typeof this.FALLBACK_RATES;
      return this.FALLBACK_RATES[fallbackKey] || 1;
    }
  }

  /**
   * Fetch exchange rate from external API
   */
  private async fetchExchangeRateFromAPI(from: string, to: string): Promise<number> {
    try {
      const response = await fetch(`${this.EXCHANGE_API_URL}/${from}`);
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (!data.rates || !data.rates[to]) {
        throw new Error(`Rate not found for ${from} to ${to}`);
      }
      
      return data.rates[to];
    } catch (error) {
      console.error('Error fetching from exchange API:', error);
      throw error;
    }
  }

  /**
   * Save exchange rate to database
   */
  private async saveExchangeRate(from: string, to: string, rate: number): Promise<void> {
    try {
      await prisma.exchangeRate.upsert({
        where: {
          fromCurrency_toCurrency: {
            fromCurrency: from,
            toCurrency: to,
          },
        },
        update: {
          rate,
          updatedAt: new Date(),
        },
        create: {
          fromCurrency: from,
          toCurrency: to,
          rate,
        },
      });
    } catch (error) {
      console.error('Error saving exchange rate:', error);
      // Don't throw here, as this is not critical
    }
  }

  /**
   * Convert amount from one currency to another
   */
  async convertCurrency(
    amount: number,
    from: string,
    to: string
  ): Promise<CurrencyConversionResult> {
    if (from === to) {
      return {
        originalAmount: amount,
        originalCurrency: from,
        convertedAmount: amount,
        convertedCurrency: to,
        exchangeRate: 1,
        lastUpdated: new Date(),
      };
    }

    const rate = await this.getExchangeRate(from, to);
    const convertedAmount = amount * rate;

    return {
      originalAmount: amount,
      originalCurrency: from,
      convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
      convertedCurrency: to,
      exchangeRate: rate,
      lastUpdated: new Date(),
    };
  }

  /**
   * Get currency based on locale
   */
  getCurrencyByLocale(locale: string): 'USD' | 'IDR' {
    switch (locale) {
      case 'id':
        return 'IDR';
      case 'en':
      default:
        return 'USD';
    }
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: string, locale?: string): string {
    const localeCode = locale || (currency === 'IDR' ? 'id-ID' : 'en-US');
    
    return new Intl.NumberFormat(localeCode, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'IDR' ? 0 : 2,
      maximumFractionDigits: currency === 'IDR' ? 0 : 2,
    }).format(amount);
  }

  /**
   * Get all supported currencies
   */
  getSupportedCurrencies(): Array<{ code: string; name: string; symbol: string }> {
    return [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp' },
    ];
  }

  /**
   * Validate currency code
   */
  isValidCurrency(currency: string): boolean {
    const supportedCurrencies = this.getSupportedCurrencies().map(c => c.code);
    return supportedCurrencies.includes(currency);
  }

  /**
   * Get exchange rates for multiple currencies
   */
  async getMultipleExchangeRates(
    baseCurrency: string,
    targetCurrencies: string[]
  ): Promise<Record<string, number>> {
    const rates: Record<string, number> = {};
    
    for (const targetCurrency of targetCurrencies) {
      try {
        rates[targetCurrency] = await this.getExchangeRate(baseCurrency, targetCurrency);
      } catch (error) {
        console.error(`Error getting rate for ${baseCurrency} to ${targetCurrency}:`, error);
        rates[targetCurrency] = 1; // Fallback
      }
    }
    
    return rates;
  }

  /**
   * Convert product prices based on user's locale
   */
  async convertProductPrice(
    priceUSD: number,
    userLocale: string
  ): Promise<{ amount: number; currency: string; exchangeRate?: number }> {
    const targetCurrency = this.getCurrencyByLocale(userLocale);
    
    if (targetCurrency === 'USD') {
      return {
        amount: priceUSD,
        currency: 'USD',
      };
    }
    
    const conversion = await this.convertCurrency(priceUSD, 'USD', targetCurrency);
    
    return {
      amount: conversion.convertedAmount,
      currency: targetCurrency,
      exchangeRate: conversion.exchangeRate,
    };
  }

  /**
   * Get currency symbol
   */
  getCurrencySymbol(currency: string): string {
    const symbols: Record<string, string> = {
      'USD': '$',
      'IDR': 'Rp',
    };
    
    return symbols[currency] || currency;
  }
}

export default new CurrencyService();
