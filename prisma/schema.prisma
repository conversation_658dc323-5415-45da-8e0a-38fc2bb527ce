generator client {
  provider      = "prisma-client-js"
  output        = "../generated/client"
  engineType    = "binary"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                String            @id @default(uuid())
  userId            String?           @unique
  firstName         String?
  lastName          String?
  email             String?           @unique
  emailVerified     DateTime?
  phoneNumber       String?           
  phoneVerified     DateTime?
  role              String            @default("user")
  oauthProvider     String?
  oauthId           String?
  password          String?
  image             String?
  isActive          Boolean           @default(true)
  isEmailVerified   Boolean           @default(false)
  isPhoneVerified   Boolean           @default(false)
  isBlocked         Boolean           @default(false)
  country           String?
  ipAddress         String?
  lastLogin         DateTime?
  lastLoginIp       String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  googleId          String?           @unique
  refreshToken      String?           @db.Text
  bids              Bid[]
  products          Product[]
  cart              Cart?
  orders            Order[]
  shippingAddresses ShippingAddress[]
}

model Category {
  id          String     @id @default(uuid())
  name        String     @unique
  description String?
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  sellType    String
  itemTypes   ItemType[]
  products    Product[]
}

model ItemType {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?
  categoryId  String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  category    Category? @relation(fields: [categoryId], references: [id])
  products    Product[]

  @@index([categoryId])
}

model Product {
  id                      String         @id @default(uuid()) @db.VarChar(300)
  itemName                String         @db.LongText
  description             String?        @db.Text
  sellType                String
  priceUSD                Decimal        @db.Decimal(10, 2)
  auctionStartDate        DateTime?
  auctionEndDate          DateTime?
  currentBid              Decimal?       @db.Decimal(10, 2)
  bidCount                Int            @default(0)
  extendedBiddingEnabled  Boolean        @default(false)
  extendedBiddingMinutes  Int?
  extendedBiddingDuration Int?
  status                  String         @default("draft")
  isActive                Boolean        @default(true)
  createdAt               DateTime       @default(now())
  updatedAt               DateTime       @updatedAt
  sellerId                String
  categoryId              String
  itemTypeId              String
  slug                    String?        @unique @db.VarChar(300)
  bids                    Bid[]
  category                Category       @relation(fields: [categoryId], references: [id])
  itemType                ItemType       @relation(fields: [itemTypeId], references: [id])
  seller                  User           @relation(fields: [sellerId], references: [id])
  images                  ProductImage[]
  cartItems               CartItem[]
  orderItems              OrderItem[]

  @@index([sellerId])
  @@index([categoryId])
  @@index([itemTypeId])
  @@index([status])
  @@index([sellType])
  @@index([slug])
}

model ProductImage {
  id        String   @id @default(uuid())
  productId String
  imageUrl  String
  altText   String?
  sortOrder Int      @default(0)
  isMain    Boolean  @default(false)
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([sortOrder])
}

model Bid {
  id        String   @id @default(uuid())
  productId String
  bidderId  String
  amount    Decimal  @db.Decimal(10, 2)
  isWinning Boolean  @default(false)
  createdAt DateTime @default(now())
  bidder    User     @relation(fields: [bidderId], references: [id])
  product   Product  @relation(fields: [productId], references: [id])

  @@index([productId])
  @@index([bidderId])
  @@index([createdAt])
}

model Cart {
  id        String     @id @default(uuid())
  userId    String     @unique
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items     CartItem[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  @@index([userId])
}

model CartItem {
  id        String   @id @default(uuid())
  cartId    String
  productId String
  quantity  Int      @default(1)
  price     Decimal  @db.Decimal(10, 2)
  cart      Cart     @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([cartId, productId])
  @@index([cartId])
  @@index([productId])
}

model ShippingAddress {
  id             String   @id @default(uuid())
  userId         String
  name           String
  address        String
  city           String
  provinceRegion String
  zipCode        String
  country        String
  isDefault      Boolean  @default(false)
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders         Order[]
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([userId])
}

model Order {
  id                String           @id @default(uuid())
  userId            String
  orderNumber       String           @unique
  status            String           @default("pending") // pending, processing, shipped, delivered, cancelled
  paymentStatus     String           @default("pending") // pending, paid, failed, refunded
  paymentMethod     String?
  subtotal          Decimal          @db.Decimal(10, 2)
  shippingCost      Decimal          @default(0) @db.Decimal(10, 2)
  tax               Decimal          @default(0) @db.Decimal(10, 2)
  total             Decimal          @db.Decimal(10, 2)
  shippingAddressId String?
  notes             String?          @db.Text
  user              User             @relation(fields: [userId], references: [id])
  shippingAddress   ShippingAddress? @relation(fields: [shippingAddressId], references: [id])
  items             OrderItem[]
  payment           Payment?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@index([userId])
  @@index([orderNumber])
  @@index([status])
  @@index([paymentStatus])
}

model OrderItem {
  id        String   @id @default(uuid())
  orderId   String
  productId String
  quantity  Int
  price     Decimal  @db.Decimal(10, 2)
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id])
  createdAt DateTime @default(now())

  @@index([orderId])
  @@index([productId])
}

model Payment {
  id              String    @id @default(uuid())
  orderId         String    @unique
  externalId      String    @unique
  xenditInvoiceId String    @unique
  amount          Decimal   @db.Decimal(10, 2)
  currency        String    @default("IDR")
  status          String    @default("PENDING") // PENDING, PAID, EXPIRED, FAILED
  invoiceUrl      String?   @db.Text
  expiryDate      DateTime?
  paidAt          DateTime?
  paidAmount      Decimal?  @db.Decimal(10, 2)
  paymentMethod   String?
  order           Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([orderId])
  @@index([status])
  @@index([xenditInvoiceId])
}
