{"language": "Bahasa", "currency": "<PERSON>", "save": "Simpan", "cancel": "Batalkan", "delete": "Hapus", "edit": "Sunting", "update": "<PERSON><PERSON><PERSON>", "add": "Tambah", "view": "Lihat", "viewAll": "<PERSON><PERSON>a", "showing": "Menampilkan", "of": "dari", "bids": "<PERSON><PERSON><PERSON>", "Navbar": {"languageCurrencyChangeText": "<PERSON>ur <PERSON> dan <PERSON>", "placeholderSearch": "<PERSON><PERSON> kole<PERSON>i..."}, "Button": {"login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>", "sellNow": "<PERSON><PERSON>", "sell": "<PERSON><PERSON>", "forgotPassword": "Lupa Kata Sandi?", "resetPassword": "Atur ulang kata sandi", "sendResetLink": "<PERSON><PERSON> atur ulang", "updateProfile": "<PERSON><PERSON><PERSON> profil", "changePassword": "Ubah kata sandi", "deleteAccount": "<PERSON><PERSON> akun", "buyNow": "<PERSON><PERSON>", "addToCart": "Tambah ke Keranjang", "placeBid": "<PERSON><PERSON>", "autoBid": "<PERSON><PERSON>", "payNow": "<PERSON><PERSON>", "createInvoice": "Buat Invoice", "viewOrder": "<PERSON><PERSON>", "refreshStatus": "Perbarui Status"}, "Product": {"title": "Produk", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "currentBid": "<PERSON><PERSON><PERSON>", "startingBid": "<PERSON><PERSON><PERSON>", "bidCount": "<PERSON><PERSON><PERSON>", "timeLeft": "<PERSON><PERSON><PERSON>", "auctionEnded": "<PERSON><PERSON>", "buyNowPrice": "<PERSON><PERSON> <PERSON>", "category": "<PERSON><PERSON><PERSON>", "condition": "<PERSON><PERSON><PERSON>", "seller": "<PERSON><PERSON><PERSON>", "addedToCart": "<PERSON><PERSON><PERSON><PERSON> ditambahkan ke keranjang", "bidPlaced": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "minimumBid": "<PERSON><PERSON><PERSON> minimum", "yourMaxBid": "<PERSON><PERSON><PERSON> maks<PERSON>", "bidIncrement": "<PERSON><PERSON><PERSON>", "autoBidEnabled": "<PERSON><PERSON> otom<PERSON>", "outbid": "<PERSON>a telah di<PERSON>", "winningBid": "Anda memiliki tawaran tertinggi", "auctionWon": "Selamat! Anda memenangkan lelang ini", "auctionLost": "Lelang berakhir. Anda tidak memenangkan item ini"}, "Payment": {"title": "Pembayaran", "selectPaymentMethod": "<PERSON><PERSON><PERSON>", "invoicePayment": "Pembayaran Invoice", "invoiceDescription": "Bayar via Invoice (Semua metode pembayaran)", "eWallet": "E-Wallet", "virtualAccount": "Virtual Account", "retailOutlet": "<PERSON><PERSON><PERSON>", "paymentSummary": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "orderId": "<PERSON> Pesanan", "paymentMethod": "<PERSON><PERSON>", "paymentStatus": "Status Pembayaran", "paymentSuccessful": "Pembayaran Berhasil", "paymentFailed": "Pembayaran Gagal", "paymentExpired": "<PERSON><PERSON><PERSON><PERSON>", "waitingForPayment": "<PERSON><PERSON><PERSON>", "timeRemaining": "<PERSON><PERSON><PERSON> ters<PERSON>", "paymentExpires": "<PERSON><PERSON><PERSON><PERSON> ked<PERSON> dalam", "loadingMethods": "Memuat metode pembayaran...", "failedToLoadMethods": "Gagal memuat metode pembayaran", "createPayment": "B<PERSON>t <PERSON>", "paymentCreated": "Pembayaran berhasil dibuat", "openPaymentPage": "<PERSON><PERSON>", "paidAt": "Dibayar <PERSON>"}, "Bidding": {"title": "Penawaran", "placeBid": "<PERSON><PERSON>", "bidAmount": "<PERSON><PERSON><PERSON>", "maxBudget": "<PERSON><PERSON><PERSON>", "incrementStep": "<PERSON><PERSON><PERSON>", "manualBid": "Tawar Manual", "autoBid": "<PERSON><PERSON>", "bidHistory": "<PERSON><PERSON><PERSON><PERSON>", "highestBidder": "Penawar Tertinggi", "yourBid": "<PERSON><PERSON><PERSON>", "bidTime": "<PERSON><PERSON><PERSON>", "bidPlaced": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "bidFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> tawaran", "invalidBidAmount": "<PERSON><PERSON><PERSON> ta<PERSON>n tidak valid", "bidTooLow": "<PERSON><PERSON><PERSON> tawaran terlalu rendah", "auctionEnded": "Lelang telah be<PERSON>hir", "autoBidSet": "<PERSON><PERSON> otomati<PERSON> ber<PERSON> diatur", "autoBidActive": "Tawar otomatis aktif", "outbidNotification": "Anda telah dikalahkan pada item ini"}, "Currency": {"usd": "USD", "idr": "IDR", "exchangeRate": "<PERSON><PERSON>", "convertedPrice": "<PERSON><PERSON>", "priceIn": "Harga dalam {currency}", "approximately": "Se<PERSON>tar"}, "Order": {"title": "<PERSON><PERSON><PERSON>", "orderNumber": "<PERSON><PERSON>", "orderDate": "<PERSON><PERSON>", "orderStatus": "<PERSON> Pesanan", "paymentStatus": "Status Pembayaran", "shippingAddress": "<PERSON><PERSON><PERSON>", "orderItems": "<PERSON><PERSON>", "subtotal": "Subtotal", "shippingCost": "<PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON>", "total": "Total", "pending": "<PERSON><PERSON><PERSON>", "processing": "Diproses", "shipped": "Di<PERSON><PERSON>", "delivered": "Diterima", "cancelled": "Di<PERSON><PERSON><PERSON>", "paid": "<PERSON><PERSON><PERSON>", "failed": "Gaga<PERSON>", "refunded": "Di<PERSON><PERSON>lik<PERSON>"}}