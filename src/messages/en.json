{"language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "update": "Update", "add": "Add", "view": "View", "viewAll": "View all", "showing": "Showing", "of": "of", "bids": "bids", "Navbar": {"languageCurrencyChangeText": "Set Language and Currency", "placeholderSearch": "Search collection..."}, "Button": {"login": "Log in", "logout": "Log out", "register": "Register", "sellNow": "Sell Now", "sell": "sell", "forgotPassword": "Forgot password?", "resetPassword": "Reset password", "sendResetLink": "Send reset link", "updateProfile": "Update profile", "changePassword": "Change password", "deleteAccount": "Delete account", "buyNow": "Buy Now", "addToCart": "Add to Cart", "placeBid": "Place Bid", "autoBid": "Auto-Bid with Limit", "payNow": "Pay Now", "createInvoice": "Create Invoice", "viewOrder": "View Order", "refreshStatus": "Refresh Status"}, "Product": {"title": "Product", "name": "Product Name", "description": "Description", "price": "Price", "currentBid": "Current Bid", "startingBid": "Starting Bid", "bidCount": "Bid <PERSON>", "timeLeft": "Time Left", "auctionEnded": "Auction Ended", "buyNowPrice": "Buy Now Price", "category": "Category", "condition": "Condition", "seller": "<PERSON><PERSON>", "addedToCart": "Added to cart successfully", "bidPlaced": "<PERSON><PERSON> placed successfully", "minimumBid": "Minimum bid", "yourMaxBid": "Your maximum bid", "bidIncrement": "Bid increment", "autoBidEnabled": "Auto-bid enabled", "outbid": "You have been outbid", "winningBid": "You have the winning bid", "auctionWon": "Congratulations! You won this auction", "auctionLost": "Auction ended. You did not win this item"}, "Payment": {"title": "Payment", "selectPaymentMethod": "Select Payment Method", "invoicePayment": "Invoice Payment", "invoiceDescription": "Pay via Invoice (All payment methods)", "eWallet": "E-Wallet", "virtualAccount": "Virtual Account", "retailOutlet": "Retail Outlet", "paymentSummary": "Payment Summary", "amount": "Amount", "orderId": "Order ID", "paymentMethod": "Payment Method", "paymentStatus": "Payment Status", "paymentSuccessful": "Payment Successful", "paymentFailed": "Payment Failed", "paymentExpired": "Payment Expired", "waitingForPayment": "Waiting for Payment", "timeRemaining": "Time remaining", "paymentExpires": "Payment expires in", "loadingMethods": "Loading payment methods...", "failedToLoadMethods": "Failed to load payment methods", "createPayment": "Create Payment", "paymentCreated": "Payment created successfully", "openPaymentPage": "Open Payment Page", "paidAt": "<PERSON><PERSON>"}, "Bidding": {"title": "Bidding", "placeBid": "Place Bid", "bidAmount": "<PERSON><PERSON>", "maxBudget": "Maximum Budget", "incrementStep": "Increment Step", "manualBid": "Manual Bid", "autoBid": "Auto-Bid", "bidHistory": "Bid History", "highestBidder": "Highest Bidder", "yourBid": "Your Bid", "bidTime": "Bid Time", "bidPlaced": "<PERSON><PERSON> placed successfully", "bidFailed": "Failed to place bid", "invalidBidAmount": "Invalid bid amount", "bidTooLow": "Bid amount is too low", "auctionEnded": "Auction has ended", "autoBidSet": "Auto-bid set successfully", "autoBidActive": "Auto-bid is active", "outbidNotification": "You have been outbid on this item"}, "Currency": {"usd": "USD", "idr": "IDR", "exchangeRate": "Exchange Rate", "convertedPrice": "Converted Price", "priceIn": "Price in {currency}", "approximately": "Approximately"}, "Order": {"title": "Order", "orderNumber": "Order Number", "orderDate": "Order Date", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "shippingAddress": "Shipping Address", "orderItems": "Order Items", "subtotal": "Subtotal", "shippingCost": "Shipping Cost", "tax": "Tax", "total": "Total", "pending": "Pending", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "paid": "Paid", "failed": "Failed", "refunded": "Refunded"}}