import { apiRequest } from './api';

export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  timestamp: string;
}

export interface CurrencyConversion {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  lastUpdated: string;
}

export interface SupportedCurrency {
  code: string;
  name: string;
  symbol: string;
}

export interface ProductPriceConversion {
  originalPrice: number;
  originalCurrency: string;
  convertedPrice: number;
  convertedCurrency: string;
  exchangeRate?: number;
  formattedPrice: string;
}

export interface CurrencyFormat {
  amount: number;
  currency: string;
  locale: string;
  formatted: string;
  symbol: string;
}

class CurrencyService {
  async getExchangeRate(from: string, to: string): Promise<ExchangeRate> {
    return apiRequest<ExchangeRate>(`/currency/exchange-rate?from=${from}&to=${to}`);
  }

  async convertCurrency(
    amount: number,
    from: string,
    to: string
  ): Promise<CurrencyConversion> {
    return apiRequest<CurrencyConversion>(
      `/currency/convert?amount=${amount}&from=${from}&to=${to}`
    );
  }

  async getSupportedCurrencies(): Promise<SupportedCurrency[]> {
    return apiRequest<SupportedCurrency[]>('/currency/supported');
  }

  async getMultipleExchangeRates(
    base: string,
    targets: string[]
  ): Promise<{ base: string; rates: Record<string, number>; timestamp: string }> {
    const targetsParam = targets.join(',');
    return apiRequest<{ base: string; rates: Record<string, number>; timestamp: string }>(
      `/currency/rates?base=${base}&targets=${targetsParam}`
    );
  }

  async convertProductPrice(
    priceUSD: number,
    locale: string
  ): Promise<ProductPriceConversion> {
    return apiRequest<ProductPriceConversion>(
      `/currency/product-price?priceUSD=${priceUSD}&locale=${locale}`
    );
  }

  async formatCurrency(
    amount: number,
    currency: string,
    locale?: string
  ): Promise<CurrencyFormat> {
    const params = new URLSearchParams({
      amount: amount.toString(),
      currency,
    });
    if (locale) params.append('locale', locale);

    return apiRequest<CurrencyFormat>(`/currency/format?${params.toString()}`);
  }
}

export const currencyService = new CurrencyService();
