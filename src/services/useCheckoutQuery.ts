import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

// Types
export interface ShippingAddress {
  id?: string;
  name: string;
  address: string;
  city: string;
  provinceRegion: string;
  zipCode: string;
  country: string;
  isDefault?: boolean;
}

export interface PaymentMethod {
  id?: string;
  type: 'credit_card' | 'debit_card' | 'paypal' | 'bank_transfer';
  cardNumber?: string;
  cardHolderName?: string;
  expiryMonth?: string;
  expiryYear?: string;
  cvv?: string;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankName?: string;
  isDefault?: boolean;
}

export interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    itemName: string;
    slug?: string;
    priceUSD: number;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
  };
}

export interface CreateOrderData {
  items: Array<{
    productId: string;
    quantity: number;
  }>;
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  notes?: string;
}

export interface BuyNowData {
  products: string[];
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  notes?: string;
}

export interface Order {
  id: string;
  userId: string;
  orderNumber: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: OrderItem[];
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CheckoutSession {
  id: string;
  items: OrderItem[];
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  expiresAt: string;
}

// Query Keys
export const checkoutQueryKeys = {
  all: ['checkout'] as const,
  session: (sessionId: string) => [...checkoutQueryKeys.all, 'session', sessionId] as const,
  orders: () => [...checkoutQueryKeys.all, 'orders'] as const,
  order: (orderId: string) => [...checkoutQueryKeys.orders(), orderId] as const,
  addresses: () => [...checkoutQueryKeys.all, 'addresses'] as const,
  paymentMethods: () => [...checkoutQueryKeys.all, 'payment-methods'] as const,
};

// Create Checkout Session (from Cart)
export const useCreateCheckoutSessionMutation = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async (): Promise<CheckoutSession> => {
      const response = await apiClient.post('/checkout/session');
      return response.data;
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create checkout session",
        type: "error",
      });
    },
  });
};

// Create Order from Checkout Session
export const useCreateOrderMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateOrderData): Promise<Order> => {
      const response = await apiClient.post('/orders', data);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.orders() });
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      
      toaster.create({
        title: "Order Created",
        description: `Order #${data.orderNumber} has been created successfully.`,
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create order",
        type: "error",
      });
    },
  });
};

// Buy Now (Direct Purchase)
export const useBuyNowMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BuyNowData): Promise<Order> => {
      const response = await apiClient.post('/checkout/orders/buy-now', data);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate orders queries
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.orders() });
      
      toaster.create({
        title: "Purchase Successful",
        description: `Order #${data.orderNumber} has been created successfully.`,
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Purchase Failed",
        description: error instanceof Error ? error.message : "Failed to complete purchase",
        type: "error",
      });
    },
  });
};

// Get User Orders
export const useOrdersQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.orders(),
    queryFn: async (): Promise<Order[]> => {
      const response = await apiClient.get('/orders');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Single Order
export const useOrderQuery = (orderId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.order(orderId),
    queryFn: async (): Promise<Order> => {
      const response = await apiClient.get(`/orders/${orderId}`);
      return response.data;
    },
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Shipping Addresses
export const useShippingAddressesQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.addresses(),
    queryFn: async (): Promise<ShippingAddress[]> => {
      const response = await apiClient.get('/user/addresses');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Create Shipping Address
export const useCreateShippingAddressMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ShippingAddress): Promise<ShippingAddress> => {
      const response = await apiClient.post('/user/addresses', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.addresses() });
      
      toaster.create({
        title: "Address Added",
        description: "Shipping address has been added successfully.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add shipping address",
        type: "error",
      });
    },
  });
};

// Get Payment Methods
export const usePaymentMethodsQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: checkoutQueryKeys.paymentMethods(),
    queryFn: async (): Promise<PaymentMethod[]> => {
      const response = await apiClient.get('/user/payment-methods');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Create Payment Method
export const useCreatePaymentMethodMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: PaymentMethod): Promise<PaymentMethod> => {
      const response = await apiClient.post('/user/payment-methods', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: checkoutQueryKeys.paymentMethods() });
      
      toaster.create({
        title: "Payment Method Added",
        description: "Payment method has been added successfully.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add payment method",
        type: "error",
      });
    },
  });
};
