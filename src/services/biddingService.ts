import { apiRequest } from './api';

export interface BidData {
  productId: string;
  amount: number;
  bidType?: 'manual' | 'auto';
}

export interface AutoBidData {
  productId: string;
  maxBudget: number;
  incrementStep: number;
}

export interface Bid {
  id: string;
  amount: number;
  isWinning: boolean;
  createdAt: string;
  bidder: {
    id: string;
    name: string;
  };
}

export interface BidHistory {
  bids: Bid[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  highestBid: number | null;
  currentWinner: {
    id: string;
    name: string;
    email: string;
  } | null;
}

export interface AutoBidSettings {
  id: string;
  productId: string;
  userId: string;
  maxBudget: number;
  incrementStep: number;
  currentBid: number | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserBidSummary {
  productId: string;
  product: {
    id: string;
    itemName: string;
    slug: string | null;
    currentBid: number | null;
    auctionEndDate: string | null;
    status: string;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
  };
  highestBid: number;
  totalBids: number;
  isWinning: boolean;
  lastBidTime: string;
  auctionStatus: 'active' | 'ended' | 'won' | 'lost';
}

export interface UserBidsResponse {
  bids: UserBidSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class BiddingService {
  async placeBid(productId: string, data: Omit<BidData, 'productId'>): Promise<any> {
    return apiRequest<any>(`/bidding/products/${productId}/bid`, {
      method: 'POST',
      body: JSON.stringify({ ...data, productId }),
    });
  }

  async setupAutoBid(productId: string, data: Omit<AutoBidData, 'productId'>): Promise<AutoBidSettings> {
    return apiRequest<AutoBidSettings>(`/bidding/products/${productId}/auto-bid`, {
      method: 'POST',
      body: JSON.stringify({ ...data, productId }),
    });
  }

  async getUserAutoBid(productId: string): Promise<AutoBidSettings | null> {
    try {
      return await apiRequest<AutoBidSettings>(`/bidding/products/${productId}/auto-bid`);
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  async deactivateAutoBid(productId: string): Promise<AutoBidSettings> {
    return apiRequest<AutoBidSettings>(`/bidding/products/${productId}/auto-bid`, {
      method: 'DELETE',
    });
  }

  async getBidHistory(
    productId: string,
    params?: { page?: number; limit?: number }
  ): Promise<BidHistory> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const query = searchParams.toString();
    return apiRequest<BidHistory>(
      `/bidding/products/${productId}/history${query ? `?${query}` : ''}`
    );
  }

  async getUserBids(params?: {
    page?: number;
    limit?: number;
    status?: 'active' | 'ended' | 'won' | 'lost';
    sortBy?: 'createdAt' | 'amount' | 'auctionEndDate';
    sortOrder?: 'asc' | 'desc';
  }): Promise<UserBidsResponse> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.status) searchParams.append('status', params.status);
    if (params?.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    const query = searchParams.toString();
    return apiRequest<UserBidsResponse>(`/bidding/user-bids${query ? `?${query}` : ''}`);
  }

  async getBiddingSummary(): Promise<{
    activeBids: number;
    wonAuctions: number;
    totalBids: number;
  }> {
    return apiRequest<{
      activeBids: number;
      wonAuctions: number;
      totalBids: number;
    }>('/bidding/summary');
  }
}

export const biddingService = new BiddingService();
