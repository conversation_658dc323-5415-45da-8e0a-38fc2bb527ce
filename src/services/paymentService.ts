import { apiRequest } from './api';

export interface CreateInvoiceData {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  customerEmail: string;
  customerName: string;
  description: string;
  successRedirectUrl?: string;
  failureRedirectUrl?: string;
}

export interface CreateEWalletData {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  ewalletType: string;
  customerPhone: string;
  customerName: string;
}

export interface InvoiceResponse {
  id: string;
  externalId: string;
  status: string;
  amount: number;
  currency: string;
  invoiceUrl: string;
  expiryDate: string;
}

export interface PaymentStatus {
  id: string;
  orderId: string;
  status: string;
  amount: number;
  currency: string;
  paidAt: string | null;
  paymentMethod: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethods {
  invoice: boolean;
  ewallet: string[];
  virtualAccount: string[];
  retailOutlet: string[];
}

class PaymentService {
  async createInvoice(data: CreateInvoiceData): Promise<InvoiceResponse> {
    return apiRequest<InvoiceResponse>('/payments/invoice', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async createEWalletCharge(data: CreateEWalletData): Promise<any> {
    return apiRequest<any>('/payments/ewallet', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getInvoiceStatus(invoiceId: string): Promise<any> {
    return apiRequest<any>(`/payments/invoice/${invoiceId}/status`);
  }

  async getPaymentStatus(orderId: string): Promise<PaymentStatus> {
    return apiRequest<PaymentStatus>(`/payments/order/${orderId}/status`);
  }

  async getPaymentMethods(currency: 'IDR' | 'USD'): Promise<PaymentMethods> {
    return apiRequest<PaymentMethods>(`/payments/methods?currency=${currency}`);
  }

  async getPaymentHistory(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.status) searchParams.append('status', params.status);

    const query = searchParams.toString();
    return apiRequest<any>(`/payments/history${query ? `?${query}` : ''}`);
  }
}

export const paymentService = new PaymentService();
