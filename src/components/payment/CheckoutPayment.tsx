'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Divider,
  useColorModeValue,
  Alert,
  AlertIcon,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Spinner,
} from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import PaymentMethodSelector from './PaymentMethodSelector';
import PaymentStatus from './PaymentStatus';

interface CheckoutPaymentProps {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  customerEmail: string;
  customerName: string;
  description: string;
  onPaymentSuccess?: () => void;
  onPaymentError?: (error: string) => void;
}

interface CreateInvoiceData {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  customerEmail: string;
  customerName: string;
  description: string;
}

interface CreateEWalletData {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  ewalletType: string;
  customerPhone: string;
  customerName: string;
}

const CheckoutPayment: React.FC<CheckoutPaymentProps> = ({
  orderId,
  amount,
  currency,
  customerEmail,
  customerName,
  description,
  onPaymentSuccess,
  onPaymentError,
}) => {
  const t = useTranslations('Payment');
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [paymentCreated, setPaymentCreated] = useState<boolean>(false);
  const [invoiceUrl, setInvoiceUrl] = useState<string>('');

  const { isOpen, onOpen, onClose } = useDisclosure();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Create Invoice Mutation
  const createInvoiceMutation = useMutation({
    mutationFn: async (data: CreateInvoiceData) => {
      const response = await fetch('/api/v1/payments/invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create invoice');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setInvoiceUrl(data.data.invoiceUrl);
      setPaymentCreated(true);
      onOpen(); // Open payment status modal
    },
    onError: (error: Error) => {
      onPaymentError?.(error.message);
    },
  });

  // Create eWallet Charge Mutation
  const createEWalletMutation = useMutation({
    mutationFn: async (data: CreateEWalletData) => {
      const response = await fetch('/api/v1/payments/ewallet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create eWallet charge');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      // Redirect to eWallet payment page
      if (data.data.actions?.desktop_web_checkout_url) {
        window.open(data.data.actions.desktop_web_checkout_url, '_blank');
      }
      setPaymentCreated(true);
      onOpen(); // Open payment status modal
    },
    onError: (error: Error) => {
      onPaymentError?.(error.message);
    },
  });

  const handleMethodSelect = (method: string, type: string) => {
    setSelectedMethod(method);
    setSelectedType(type);
  };

  const handlePayment = () => {
    if (!selectedMethod || !selectedType) {
      onPaymentError?.('Please select a payment method');
      return;
    }

    if (selectedType === 'invoice') {
      createInvoiceMutation.mutate({
        orderId,
        amount,
        currency,
        customerEmail,
        customerName,
        description,
      });
    } else if (selectedType === 'ewallet') {
      // For eWallet, we need phone number - this should be collected from user
      const customerPhone = '+62812345678'; // This should come from user input
      
      createEWalletMutation.mutate({
        orderId,
        amount,
        currency,
        ewalletType: selectedMethod,
        customerPhone,
        customerName,
      });
    }
  };

  const handlePaymentComplete = () => {
    onPaymentSuccess?.();
    onClose();
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat(currency === 'IDR' ? 'id-ID' : 'en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const isLoading = createInvoiceMutation.isPending || createEWalletMutation.isPending;

  return (
    <>
      <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
        <VStack spacing={6} align="stretch">
          {/* Payment Summary */}
          <Box>
            <Text fontSize="xl" fontWeight="bold" mb={4}>
              Payment Summary
            </Text>
            <VStack spacing={3} align="stretch">
              <HStack justify="space-between">
                <Text color="gray.600">Order ID:</Text>
                <Text fontFamily="mono" fontSize="sm">{orderId}</Text>
              </HStack>
              <HStack justify="space-between">
                <Text color="gray.600">Amount:</Text>
                <Text fontWeight="bold" fontSize="lg">
                  {formatCurrency(amount, currency)}
                </Text>
              </HStack>
              <HStack justify="space-between">
                <Text color="gray.600">Description:</Text>
                <Text>{description}</Text>
              </HStack>
            </VStack>
          </Box>

          <Divider />

          {/* Payment Method Selection */}
          <PaymentMethodSelector
            currency={currency}
            onMethodSelect={handleMethodSelect}
            selectedMethod={selectedMethod}
          />

          {/* Error Display */}
          {(createInvoiceMutation.error || createEWalletMutation.error) && (
            <Alert status="error">
              <AlertIcon />
              {createInvoiceMutation.error?.message || createEWalletMutation.error?.message}
            </Alert>
          )}

          {/* Payment Button */}
          <Button
            colorScheme="blue"
            size="lg"
            onClick={handlePayment}
            isLoading={isLoading}
            loadingText="Creating Payment..."
            isDisabled={!selectedMethod || !selectedType}
          >
            {selectedType === 'invoice' ? 'Create Invoice' : 'Pay Now'}
          </Button>

          {/* Invoice URL Display */}
          {invoiceUrl && (
            <Alert status="success">
              <AlertIcon />
              <VStack align="start" spacing={2}>
                <Text>Payment invoice created successfully!</Text>
                <Button
                  size="sm"
                  colorScheme="blue"
                  onClick={() => window.open(invoiceUrl, '_blank')}
                >
                  Open Payment Page
                </Button>
              </VStack>
            </Alert>
          )}
        </VStack>
      </Box>

      {/* Payment Status Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg" closeOnOverlayClick={false}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Payment Status</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {paymentCreated && (
              <PaymentStatus
                orderId={orderId}
                onPaymentComplete={handlePaymentComplete}
                autoRefresh={true}
                refreshInterval={5000}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default CheckoutPayment;
