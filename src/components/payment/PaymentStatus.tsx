'use client';

import React, { useEffect, useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  <PERSON>,
  <PERSON><PERSON>,
  Spin<PERSON>,
  <PERSON>ert,
  AlertIcon,
  Badge,
  useColorModeValue,
  Icon,
  Progress,
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { CheckCircleIcon, WarningIcon, TimeIcon } from '@chakra-ui/icons';

interface PaymentStatusProps {
  orderId: string;
  onPaymentComplete?: () => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface PaymentData {
  id: string;
  orderId: string;
  status: string;
  amount: number;
  currency: string;
  paidAt: string | null;
  paymentMethod: string | null;
  createdAt: string;
  updatedAt: string;
}

const PaymentStatus: React.FC<PaymentStatusProps> = ({
  orderId,
  onPaymentComplete,
  autoRefresh = true,
  refreshInterval = 5000,
}) => {
  const t = useTranslations('Payment');
  const [timeLeft, setTimeLeft] = useState<number>(0);

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Fetch payment status
  const { data: payment, isLoading, refetch } = useQuery<PaymentData>({
    queryKey: ['paymentStatus', orderId],
    queryFn: async () => {
      const response = await fetch(`/api/v1/payments/order/${orderId}/status`);
      if (!response.ok) throw new Error('Failed to fetch payment status');
      const result = await response.json();
      return result.data;
    },
    refetchInterval: autoRefresh ? refreshInterval : false,
    enabled: !!orderId,
  });

  // Auto refresh and callback on payment complete
  useEffect(() => {
    if (payment?.status === 'PAID' && onPaymentComplete) {
      onPaymentComplete();
    }
  }, [payment?.status, onPaymentComplete]);

  // Calculate time left for payment (24 hours from creation)
  useEffect(() => {
    if (payment?.createdAt && payment.status === 'PENDING') {
      const createdTime = new Date(payment.createdAt).getTime();
      const expiryTime = createdTime + (24 * 60 * 60 * 1000); // 24 hours
      
      const interval = setInterval(() => {
        const now = Date.now();
        const remaining = Math.max(0, expiryTime - now);
        setTimeLeft(remaining);
        
        if (remaining === 0) {
          clearInterval(interval);
          refetch(); // Refetch to get updated status
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [payment?.createdAt, payment?.status, refetch]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return <CheckCircleIcon color="green.500" boxSize={6} />;
      case 'EXPIRED':
      case 'FAILED':
        return <WarningIcon color="red.500" boxSize={6} />;
      case 'PENDING':
      default:
        return <TimeIcon color="orange.500" boxSize={6} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'green';
      case 'EXPIRED':
      case 'FAILED':
        return 'red';
      case 'PENDING':
      default:
        return 'orange';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'Payment Successful';
      case 'EXPIRED':
        return 'Payment Expired';
      case 'FAILED':
        return 'Payment Failed';
      case 'PENDING':
      default:
        return 'Waiting for Payment';
    }
  };

  const formatTimeLeft = (milliseconds: number) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat(currency === 'IDR' ? 'id-ID' : 'en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
        <VStack spacing={4}>
          <Spinner size="lg" />
          <Text>Checking payment status...</Text>
        </VStack>
      </Box>
    );
  }

  if (!payment) {
    return (
      <Alert status="error">
        <AlertIcon />
        Payment information not found
      </Alert>
    );
  }

  return (
    <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
      <VStack spacing={6} align="stretch">
        {/* Status Header */}
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            {getStatusIcon(payment.status)}
            <VStack align="start" spacing={0}>
              <Text fontSize="lg" fontWeight="bold">
                {getStatusText(payment.status)}
              </Text>
              <Badge colorScheme={getStatusColor(payment.status)} size="sm">
                {payment.status}
              </Badge>
            </VStack>
          </HStack>
          
          {payment.status === 'PENDING' && timeLeft > 0 && (
            <VStack align="end" spacing={1}>
              <Text fontSize="sm" color="gray.600">
                Time remaining
              </Text>
              <Text fontSize="lg" fontWeight="bold" color="orange.500">
                {formatTimeLeft(timeLeft)}
              </Text>
            </VStack>
          )}
        </HStack>

        {/* Payment Details */}
        <VStack spacing={3} align="stretch">
          <HStack justify="space-between">
            <Text color="gray.600">Amount:</Text>
            <Text fontWeight="bold" fontSize="lg">
              {formatCurrency(payment.amount, payment.currency)}
            </Text>
          </HStack>
          
          <HStack justify="space-between">
            <Text color="gray.600">Order ID:</Text>
            <Text fontFamily="mono" fontSize="sm">
              {payment.orderId}
            </Text>
          </HStack>

          {payment.paymentMethod && (
            <HStack justify="space-between">
              <Text color="gray.600">Payment Method:</Text>
              <Text>{payment.paymentMethod}</Text>
            </HStack>
          )}

          {payment.paidAt && (
            <HStack justify="space-between">
              <Text color="gray.600">Paid At:</Text>
              <Text>{new Date(payment.paidAt).toLocaleString()}</Text>
            </HStack>
          )}
        </VStack>

        {/* Progress Bar for Pending Payments */}
        {payment.status === 'PENDING' && timeLeft > 0 && (
          <Box>
            <Text fontSize="sm" color="gray.600" mb={2}>
              Payment expires in {formatTimeLeft(timeLeft)}
            </Text>
            <Progress 
              value={(timeLeft / (24 * 60 * 60 * 1000)) * 100} 
              colorScheme="orange" 
              size="sm" 
              borderRadius="md"
            />
          </Box>
        )}

        {/* Action Buttons */}
        <HStack spacing={3}>
          <Button
            size="sm"
            variant="outline"
            onClick={() => refetch()}
            isLoading={isLoading}
          >
            Refresh Status
          </Button>
          
          {payment.status === 'PAID' && (
            <Button
              size="sm"
              colorScheme="green"
              onClick={() => window.location.href = `/orders/${orderId}`}
            >
              View Order
            </Button>
          )}
        </HStack>

        {/* Status Messages */}
        {payment.status === 'PENDING' && (
          <Alert status="info">
            <AlertIcon />
            Please complete your payment. This page will automatically update when payment is received.
          </Alert>
        )}

        {payment.status === 'EXPIRED' && (
          <Alert status="warning">
            <AlertIcon />
            Payment has expired. Please create a new order to continue.
          </Alert>
        )}

        {payment.status === 'FAILED' && (
          <Alert status="error">
            <AlertIcon />
            Payment failed. Please try again or contact support.
          </Alert>
        )}
      </VStack>
    </Box>
  );
};

export default PaymentStatus;
