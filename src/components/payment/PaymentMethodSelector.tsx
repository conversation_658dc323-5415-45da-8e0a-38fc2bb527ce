'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Radio,
  RadioGroup,
  Stack,
  Image,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useLocale, useTranslations } from 'next-intl';

interface PaymentMethod {
  invoice: boolean;
  ewallet: string[];
  virtualAccount: string[];
  retailOutlet: string[];
}

interface PaymentMethodSelectorProps {
  currency: 'IDR' | 'USD';
  onMethodSelect: (method: string, type: string) => void;
  selectedMethod?: string;
  isLoading?: boolean;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  currency,
  onMethodSelect,
  selectedMethod,
  isLoading = false,
}) => {
  const t = useTranslations('Payment');
  const locale = useLocale();
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedProvider, setSelectedProvider] = useState<string>('');

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Fetch available payment methods
  const { data: paymentMethods, isLoading: methodsLoading } = useQuery<PaymentMethod>({
    queryKey: ['paymentMethods', currency],
    queryFn: async () => {
      const response = await fetch(`/api/v1/payments/methods?currency=${currency}`);
      if (!response.ok) throw new Error('Failed to fetch payment methods');
      const result = await response.json();
      return result.data;
    },
  });

  const handleMethodChange = (type: string, provider?: string) => {
    setSelectedType(type);
    setSelectedProvider(provider || '');
    onMethodSelect(provider || type, type);
  };

  const getMethodIcon = (method: string) => {
    const icons: { [key: string]: string } = {
      invoice: '/icons/invoice.png',
      OVO: '/icons/ovo.png',
      DANA: '/icons/dana.png',
      LINKAJA: '/icons/linkaja.png',
      SHOPEEPAY: '/icons/shopeepay.png',
      BCA: '/icons/bca.png',
      BNI: '/icons/bni.png',
      BRI: '/icons/bri.png',
      MANDIRI: '/icons/mandiri.png',
      ALFAMART: '/icons/alfamart.png',
      INDOMARET: '/icons/indomaret.png',
    };
    return icons[method] || '/icons/default-payment.png';
  };

  if (methodsLoading) {
    return (
      <Box p={4} textAlign="center">
        <Spinner size="lg" />
        <Text mt={2}>{t('loadingMethods')}</Text>
      </Box>
    );
  }

  if (!paymentMethods) {
    return (
      <Alert status="error">
        <AlertIcon />
        {t('failedToLoadMethods')}
      </Alert>
    );
  }

  return (
    <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        {t('selectPaymentMethod')}
      </Text>

      <VStack spacing={4} align="stretch">
        {/* Invoice Payment */}
        {paymentMethods.invoice && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('invoicePayment')}
            </Text>
            <Box
              p={3}
              border="1px"
              borderColor={selectedType === 'invoice' ? 'blue.500' : borderColor}
              borderRadius="md"
              cursor="pointer"
              onClick={() => handleMethodChange('invoice')}
              bg={selectedType === 'invoice' ? 'blue.50' : 'transparent'}
            >
              <HStack>
                <Radio
                  isChecked={selectedType === 'invoice'}
                  onChange={() => handleMethodChange('invoice')}
                />
                <Image src={getMethodIcon('invoice')} alt="Invoice" boxSize="24px" />
                <Text>{t('invoiceDescription')}</Text>
              </HStack>
            </Box>
          </Box>
        )}

        {/* E-Wallet */}
        {paymentMethods.ewallet.length > 0 && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('eWallet')}
            </Text>
            <RadioGroup value={selectedProvider} onChange={(value) => handleMethodChange('ewallet', value)}>
              <Stack spacing={2}>
                {paymentMethods.ewallet.map((wallet) => (
                  <Box
                    key={wallet}
                    p={3}
                    border="1px"
                    borderColor={selectedProvider === wallet ? 'blue.500' : borderColor}
                    borderRadius="md"
                    cursor="pointer"
                    onClick={() => handleMethodChange('ewallet', wallet)}
                    bg={selectedProvider === wallet ? 'blue.50' : 'transparent'}
                  >
                    <HStack>
                      <Radio value={wallet} />
                      <Image src={getMethodIcon(wallet)} alt={wallet} boxSize="24px" />
                      <Text>{wallet}</Text>
                    </HStack>
                  </Box>
                ))}
              </Stack>
            </RadioGroup>
          </Box>
        )}

        {/* Virtual Account */}
        {paymentMethods.virtualAccount.length > 0 && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('virtualAccount')}
            </Text>
            <RadioGroup value={selectedProvider} onChange={(value) => handleMethodChange('virtualAccount', value)}>
              <Stack spacing={2}>
                {paymentMethods.virtualAccount.map((bank) => (
                  <Box
                    key={bank}
                    p={3}
                    border="1px"
                    borderColor={selectedProvider === bank ? 'blue.500' : borderColor}
                    borderRadius="md"
                    cursor="pointer"
                    onClick={() => handleMethodChange('virtualAccount', bank)}
                    bg={selectedProvider === bank ? 'blue.50' : 'transparent'}
                  >
                    <HStack>
                      <Radio value={bank} />
                      <Image src={getMethodIcon(bank)} alt={bank} boxSize="24px" />
                      <Text>{bank}</Text>
                    </HStack>
                  </Box>
                ))}
              </Stack>
            </RadioGroup>
          </Box>
        )}

        {/* Retail Outlet */}
        {paymentMethods.retailOutlet.length > 0 && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('retailOutlet')}
            </Text>
            <RadioGroup value={selectedProvider} onChange={(value) => handleMethodChange('retailOutlet', value)}>
              <Stack spacing={2}>
                {paymentMethods.retailOutlet.map((outlet) => (
                  <Box
                    key={outlet}
                    p={3}
                    border="1px"
                    borderColor={selectedProvider === outlet ? 'blue.500' : borderColor}
                    borderRadius="md"
                    cursor="pointer"
                    onClick={() => handleMethodChange('retailOutlet', outlet)}
                    bg={selectedProvider === outlet ? 'blue.50' : 'transparent'}
                  >
                    <HStack>
                      <Radio value={outlet} />
                      <Image src={getMethodIcon(outlet)} alt={outlet} boxSize="24px" />
                      <Text>{outlet}</Text>
                    </HStack>
                  </Box>
                ))}
              </Stack>
            </RadioGroup>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default PaymentMethodSelector;
