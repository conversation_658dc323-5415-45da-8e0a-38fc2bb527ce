'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Badge,
  Alert,
  AlertIcon,
  useColorModeValue,
  useToast,
} from '@chakra-ui/react';
import { useTranslations } from 'next-intl';
import ManualBidForm from './ManualBidForm';
import AutoBidForm from './AutoBidForm';
import BidUpdates from './BidUpdates';
import CurrencyDisplay from '../ui/CurrencyDisplay';

interface BiddingInterfaceProps {
  productId: string;
  currentBid: number;
  startingPrice: number;
  auctionEndDate: string | null;
  isActive: boolean;
  currency?: 'USD' | 'IDR';
  isOwner?: boolean;
}

const BiddingInterface: React.FC<BiddingInterfaceProps> = ({
  productId,
  currentBid,
  startingPrice,
  auctionEndDate,
  isActive,
  currency = 'USD',
  isOwner = false,
}) => {
  const t = useTranslations('Bidding');
  const toast = useToast();
  const [activeTab, setActiveTab] = useState(0);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const minimumBid = currentBid > 0 ? currentBid + 1 : startingPrice;
  const isAuctionEnded = auctionEndDate ? new Date() > new Date(auctionEndDate) : false;

  const handleBidSuccess = (bidAmount: number) => {
    toast({
      title: t('bidPlaced'),
      description: `Your bid of ${bidAmount} has been placed successfully`,
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  const handleBidError = (error: string) => {
    toast({
      title: t('bidFailed'),
      description: error,
      status: 'error',
      duration: 5000,
      isClosable: true,
    });
  };

  const handleAutoBidSuccess = () => {
    toast({
      title: t('autoBidSet'),
      description: 'Auto-bid has been set up successfully',
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  const formatTimeLeft = () => {
    if (!auctionEndDate) return null;
    
    const endDate = new Date(auctionEndDate);
    const now = new Date();
    const timeLeft = endDate.getTime() - now.getTime();
    
    if (timeLeft <= 0) return 'Auction ended';
    
    const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  // Don't show bidding interface if user is the owner
  if (isOwner) {
    return (
      <Alert status="info">
        <AlertIcon />
        You cannot bid on your own product
      </Alert>
    );
  }

  // Don't show bidding interface if auction has ended
  if (isAuctionEnded) {
    return (
      <Alert status="warning">
        <AlertIcon />
        {t('auctionEnded')}
      </Alert>
    );
  }

  // Don't show bidding interface if auction is not active
  if (!isActive) {
    return (
      <Alert status="info">
        <AlertIcon />
        This auction is not currently active
      </Alert>
    );
  }

  return (
    <VStack spacing={6} align="stretch">
      {/* Auction Info */}
      <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
        <VStack spacing={4} align="stretch">
          <HStack justify="space-between">
            <Text fontSize="xl" fontWeight="bold">
              Current Auction
            </Text>
            <Badge colorScheme="green" size="lg">
              Live
            </Badge>
          </HStack>
          
          <VStack spacing={3} align="stretch">
            <HStack justify="space-between">
              <Text color="gray.600">Current Bid:</Text>
              <Text fontSize="xl" fontWeight="bold">
                <CurrencyDisplay amount={currentBid || startingPrice} baseCurrency={currency} />
              </Text>
            </HStack>
            
            <HStack justify="space-between">
              <Text color="gray.600">Minimum Bid:</Text>
              <Text fontSize="lg" fontWeight="semibold" color="blue.600">
                <CurrencyDisplay amount={minimumBid} baseCurrency={currency} />
              </Text>
            </HStack>
            
            {auctionEndDate && (
              <HStack justify="space-between">
                <Text color="gray.600">Time Left:</Text>
                <Text fontSize="lg" fontWeight="semibold" color="orange.600">
                  {formatTimeLeft()}
                </Text>
              </HStack>
            )}
          </VStack>
        </VStack>
      </Box>

      {/* Bidding Interface */}
      <Tabs index={activeTab} onChange={setActiveTab} variant="enclosed">
        <TabList>
          <Tab>{t('manualBid')}</Tab>
          <Tab>{t('autoBid')}</Tab>
        </TabList>

        <TabPanels>
          <TabPanel p={0} pt={4}>
            <ManualBidForm
              productId={productId}
              currentBid={currentBid || startingPrice}
              minimumBid={minimumBid}
              currency={currency}
              onBidSuccess={handleBidSuccess}
              onBidError={handleBidError}
            />
          </TabPanel>
          
          <TabPanel p={0} pt={4}>
            <AutoBidForm
              productId={productId}
              currentBid={currentBid || startingPrice}
              minimumBid={minimumBid}
              currency={currency}
              onAutoBidSuccess={handleAutoBidSuccess}
              onAutoBidError={handleBidError}
            />
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Bid Updates */}
      <BidUpdates
        productId={productId}
        autoRefresh={true}
        refreshInterval={5000}
        maxBids={10}
      />
    </VStack>
  );
};

export default BiddingInterface;
