'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Button,
  FormControl,
  FormLabel,
  FormErrorMessage,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Alert,
  AlertIcon,
  useColorModeValue,
  Divider,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import CurrencyDisplay from '../ui/CurrencyDisplay';

interface ManualBidFormProps {
  productId: string;
  currentBid: number;
  minimumBid: number;
  currency?: 'USD' | 'IDR';
  onBidSuccess?: (bidAmount: number) => void;
  onBidError?: (error: string) => void;
}

interface BidFormData {
  amount: number;
}

const ManualBidForm: React.FC<ManualBidFormProps> = ({
  productId,
  currentBid,
  minimumBid,
  currency = 'USD',
  onBidSuccess,
  onBidError,
}) => {
  const t = useTranslations('Bidding');
  const queryClient = useQueryClient();
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<BidFormData>({
    defaultValues: {
      amount: minimumBid,
    },
  });

  const bidAmount = watch('amount');

  // Place bid mutation
  const placeBidMutation = useMutation({
    mutationFn: async (data: BidFormData) => {
      const response = await fetch(`/api/v1/bidding/products/${productId}/bid`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          bidType: 'manual',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to place bid');
      }

      return response.json();
    },
    onSuccess: (data) => {
      onBidSuccess?.(bidAmount);
      reset();
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['product', productId] });
      queryClient.invalidateQueries({ queryKey: ['bidHistory', productId] });
    },
    onError: (error: Error) => {
      onBidError?.(error.message);
    },
  });

  const onSubmit = (data: BidFormData) => {
    placeBidMutation.mutate(data);
  };

  const handleQuickBid = (amount: number) => {
    setValue('amount', amount);
  };

  const quickBidAmounts = [
    minimumBid,
    minimumBid + 5,
    minimumBid + 10,
    minimumBid + 25,
  ];

  return (
    <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
      <VStack spacing={6} align="stretch">
        {/* Current Bid Info */}
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={2}>
            {t('placeBid')}
          </Text>
          <VStack spacing={2} align="stretch">
            <HStack justify="space-between">
              <Text color="gray.600">{t('currentBid')}:</Text>
              <CurrencyDisplay amount={currentBid} baseCurrency={currency} />
            </HStack>
            <HStack justify="space-between">
              <Text color="gray.600">{t('minimumBid')}:</Text>
              <CurrencyDisplay amount={minimumBid} baseCurrency={currency} />
            </HStack>
          </VStack>
        </Box>

        <Divider />

        {/* Bid Form */}
        <form onSubmit={handleSubmit(onSubmit)}>
          <VStack spacing={4} align="stretch">
            <FormControl isInvalid={!!errors.amount}>
              <FormLabel>{t('bidAmount')}</FormLabel>
              <NumberInput
                min={minimumBid}
                precision={2}
                value={bidAmount}
                onChange={(valueString) => setValue('amount', parseFloat(valueString) || minimumBid)}
              >
                <NumberInputField
                  {...register('amount', {
                    required: 'Bid amount is required',
                    min: {
                      value: minimumBid,
                      message: `Minimum bid is ${minimumBid}`,
                    },
                  })}
                />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
              <FormErrorMessage>{errors.amount?.message}</FormErrorMessage>
            </FormControl>

            {/* Quick Bid Buttons */}
            <Box>
              <Text fontSize="sm" color="gray.600" mb={2}>
                Quick bid amounts:
              </Text>
              <HStack spacing={2} flexWrap="wrap">
                {quickBidAmounts.map((amount) => (
                  <Button
                    key={amount}
                    size="sm"
                    variant="outline"
                    onClick={() => handleQuickBid(amount)}
                    isDisabled={amount < minimumBid}
                  >
                    <CurrencyDisplay 
                      amount={amount} 
                      baseCurrency={currency}
                      size="sm"
                      showTooltip={false}
                    />
                  </Button>
                ))}
              </HStack>
            </Box>

            {/* Bid Preview */}
            {bidAmount > 0 && (
              <Box p={3} bg="blue.50" borderRadius="md" _dark={{ bg: 'blue.900' }}>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="blue.700" _dark={{ color: 'blue.300' }}>
                    Your bid:
                  </Text>
                  <Text fontSize="lg" fontWeight="bold" color="blue.700" _dark={{ color: 'blue.300' }}>
                    <CurrencyDisplay 
                      amount={bidAmount} 
                      baseCurrency={currency}
                      showTooltip={false}
                    />
                  </Text>
                </HStack>
              </Box>
            )}

            {/* Error Display */}
            {placeBidMutation.error && (
              <Alert status="error">
                <AlertIcon />
                {placeBidMutation.error.message}
              </Alert>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              colorScheme="blue"
              size="lg"
              isLoading={placeBidMutation.isPending}
              loadingText="Placing Bid..."
              isDisabled={!bidAmount || bidAmount < minimumBid}
            >
              {t('placeBid')} - <CurrencyDisplay 
                amount={bidAmount || 0} 
                baseCurrency={currency}
                showTooltip={false}
              />
            </Button>
          </VStack>
        </form>

        {/* Bid Guidelines */}
        <Box p={3} bg="gray.50" borderRadius="md" _dark={{ bg: 'gray.700' }}>
          <Text fontSize="xs" color="gray.600" _dark={{ color: 'gray.400' }}>
            • Bids are binding and cannot be retracted
            • You will be notified if you are outbid
            • Payment is required within 24 hours if you win
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};

export default ManualBidForm;
