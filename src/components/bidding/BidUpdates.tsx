'use client';

import React, { useEffect, useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Avatar,
  Spinner,
  Alert,
  AlertIcon,
  useColorModeValue,
  Divider,
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import CurrencyDisplay from '../ui/CurrencyDisplay';

interface BidUpdatesProps {
  productId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  maxBids?: number;
}

interface Bid {
  id: string;
  amount: number;
  isWinning: boolean;
  createdAt: string;
  bidder: {
    id: string;
    name: string;
  };
}

interface BidHistory {
  bids: Bid[];
  totalBids: number;
  highestBid: number | null;
  currentWinner: {
    id: string;
    name: string;
    email: string;
  } | null;
}

const BidUpdates: React.FC<BidUpdatesProps> = ({
  productId,
  autoRefresh = true,
  refreshInterval = 5000,
  maxBids = 10,
}) => {
  const t = useTranslations('Bidding');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Fetch bid history with auto-refresh
  const { data: bidHistory, isLoading, error } = useQuery<BidHistory>({
    queryKey: ['bidHistory', productId],
    queryFn: async () => {
      const response = await fetch(`/api/v1/bidding/products/${productId}/history?limit=${maxBids}`);
      if (!response.ok) throw new Error('Failed to fetch bid history');
      const result = await response.json();
      return result.data;
    },
    refetchInterval: autoRefresh ? refreshInterval : false,
    refetchOnWindowFocus: true,
  });

  // Update last update time when data changes
  useEffect(() => {
    if (bidHistory) {
      setLastUpdate(new Date());
    }
  }, [bidHistory]);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  const getBidderInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
        <VStack spacing={4}>
          <Spinner size="lg" />
          <Text>Loading bid updates...</Text>
        </VStack>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        Failed to load bid updates
      </Alert>
    );
  }

  if (!bidHistory || bidHistory.bids.length === 0) {
    return (
      <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
        <Text textAlign="center" color={mutedColor}>
          No bids yet. Be the first to bid!
        </Text>
      </Box>
    );
  }

  return (
    <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
      <VStack spacing={4} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Text fontSize="lg" fontWeight="bold">
            {t('bidHistory')} ({bidHistory.totalBids})
          </Text>
          <HStack spacing={2}>
            {autoRefresh && (
              <Badge colorScheme="green" size="sm">
                Live
              </Badge>
            )}
            <Text fontSize="xs" color={mutedColor}>
              Updated {formatTimeAgo(lastUpdate.toISOString())}
            </Text>
          </HStack>
        </HStack>

        {/* Current Winner */}
        {bidHistory.currentWinner && bidHistory.highestBid && (
          <Box p={3} bg="green.50" borderRadius="md" _dark={{ bg: 'green.900' }}>
            <HStack justify="space-between">
              <HStack spacing={3}>
                <Avatar
                  size="sm"
                  name={bidHistory.currentWinner.name}
                  bg="green.500"
                  color="white"
                />
                <VStack align="start" spacing={0}>
                  <Text fontSize="sm" fontWeight="semibold" color="green.700" _dark={{ color: 'green.300' }}>
                    {t('highestBidder')}
                  </Text>
                  <Text fontSize="xs" color="green.600" _dark={{ color: 'green.400' }}>
                    {bidHistory.currentWinner.name}
                  </Text>
                </VStack>
              </HStack>
              <VStack align="end" spacing={0}>
                <Text fontSize="lg" fontWeight="bold" color="green.700" _dark={{ color: 'green.300' }}>
                  <CurrencyDisplay 
                    amount={bidHistory.highestBid} 
                    showTooltip={false}
                  />
                </Text>
                <Badge colorScheme="green" size="sm">
                  Winning
                </Badge>
              </VStack>
            </HStack>
          </Box>
        )}

        <Divider />

        {/* Bid List */}
        <VStack spacing={3} align="stretch" maxH="400px" overflowY="auto">
          {bidHistory.bids.map((bid, index) => (
            <HStack key={bid.id} justify="space-between" p={3} borderRadius="md" bg={index === 0 ? 'blue.50' : 'transparent'} _dark={{ bg: index === 0 ? 'blue.900' : 'transparent' }}>
              <HStack spacing={3}>
                <Avatar
                  size="sm"
                  name={bid.bidder.name}
                  bg={bid.isWinning ? 'green.500' : 'gray.500'}
                  color="white"
                />
                <VStack align="start" spacing={0}>
                  <Text fontSize="sm" fontWeight="medium">
                    {bid.bidder.name}
                  </Text>
                  <Text fontSize="xs" color={mutedColor}>
                    {formatTimeAgo(bid.createdAt)}
                  </Text>
                </VStack>
              </HStack>
              
              <VStack align="end" spacing={0}>
                <Text fontSize="md" fontWeight="semibold">
                  <CurrencyDisplay 
                    amount={bid.amount} 
                    showTooltip={false}
                  />
                </Text>
                {bid.isWinning && (
                  <Badge colorScheme="green" size="sm">
                    Winning
                  </Badge>
                )}
                {index === 0 && !bid.isWinning && (
                  <Badge colorScheme="blue" size="sm">
                    Latest
                  </Badge>
                )}
              </VStack>
            </HStack>
          ))}
        </VStack>

        {/* Show More */}
        {bidHistory.totalBids > maxBids && (
          <Text fontSize="sm" color={mutedColor} textAlign="center">
            Showing {Math.min(maxBids, bidHistory.bids.length)} of {bidHistory.totalBids} bids
          </Text>
        )}

        {/* Auto-refresh indicator */}
        {autoRefresh && (
          <HStack justify="center" spacing={2}>
            <Box w={2} h={2} bg="green.500" borderRadius="full" />
            <Text fontSize="xs" color={mutedColor}>
              Auto-updating every {refreshInterval / 1000}s
            </Text>
          </HStack>
        )}
      </VStack>
    </Box>
  );
};

export default BidUpdates;
