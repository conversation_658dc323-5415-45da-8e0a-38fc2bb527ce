'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Field,
  NumberInputField,
  NumberInputRoot,
  Alert,
  Badge,
  Switch,
  Separator,
  Tooltip,
} from '@chakra-ui/react';
import { HiInformationCircle } from 'react-icons/hi2';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import CurrencyDisplay from '../ui/CurrencyDisplay';

interface AutoBidFormProps {
  productId: string;
  currentBid: number;
  minimumBid: number;
  currency?: 'USD' | 'IDR';
  onAutoBidSuccess?: () => void;
  onAutoBidError?: (error: string) => void;
}

interface AutoBidFormData {
  maxBudget: number;
  incrementStep: number;
}

interface AutoBidSettings {
  id: string;
  maxBudget: number;
  incrementStep: number;
  currentBid: number | null;
  isActive: boolean;
}

const AutoBidForm: React.FC<AutoBidFormProps> = ({
  productId,
  currentBid,
  minimumBid,
  currency = 'USD',
  onAutoBidSuccess,
  onAutoBidError,
}) => {
  const t = useTranslations('Bidding');
  const queryClient = useQueryClient();
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<AutoBidFormData>({
    defaultValues: {
      maxBudget: minimumBid + 10,
      incrementStep: 1,
    },
  });

  const maxBudget = watch('maxBudget');
  const incrementStep = watch('incrementStep');

  // Fetch existing auto-bid settings
  const { data: autoBidSettings } = useQuery<AutoBidSettings>({
    queryKey: ['autoBid', productId],
    queryFn: async () => {
      const response = await fetch(`/api/v1/bidding/products/${productId}/auto-bid`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Failed to fetch auto-bid settings');
      }
      const result = await response.json();
      return result.data;
    },
  });

  // Setup auto-bid mutation
  const setupAutoBidMutation = useMutation({
    mutationFn: async (data: AutoBidFormData) => {
      const response = await fetch(`/api/v1/bidding/products/${productId}/auto-bid`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to setup auto-bid');
      }

      return response.json();
    },
    onSuccess: () => {
      onAutoBidSuccess?.();
      queryClient.invalidateQueries({ queryKey: ['autoBid', productId] });
    },
    onError: (error: Error) => {
      onAutoBidError?.(error.message);
    },
  });

  // Deactivate auto-bid mutation
  const deactivateAutoBidMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/v1/bidding/products/${productId}/auto-bid`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to deactivate auto-bid');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['autoBid', productId] });
    },
  });

  const onSubmit = (data: AutoBidFormData) => {
    setupAutoBidMutation.mutate(data);
  };

  const handleDeactivate = () => {
    deactivateAutoBidMutation.mutate();
  };

  const calculatePotentialBids = () => {
    if (!maxBudget || !incrementStep) return [];
    
    const bids = [];
    let currentAmount = Math.max(currentBid + incrementStep, minimumBid);
    
    while (currentAmount <= maxBudget && bids.length < 5) {
      bids.push(currentAmount);
      currentAmount += incrementStep;
    }
    
    return bids;
  };

  const potentialBids = calculatePotentialBids();

  return (
    <Box bg={bgColor} border="1px" borderColor={borderColor} borderRadius="lg" p={6}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <HStack spacing={2}>
            <Text fontSize="lg" fontWeight="bold">
              {t('autoBid')}
            </Text>
            <Tooltip label="Auto-bid will automatically place bids for you up to your maximum budget">
              <Icon as={InfoIcon} color="gray.500" />
            </Tooltip>
          </HStack>
          
          {autoBidSettings?.isActive && (
            <Badge colorScheme="green" size="sm">
              {t('autoBidActive')}
            </Badge>
          )}
        </HStack>

        {/* Current Auto-bid Status */}
        {autoBidSettings && (
          <Box p={3} bg="blue.50" borderRadius="md" _dark={{ bg: 'blue.900' }}>
            <VStack spacing={2} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="sm" color="blue.700" _dark={{ color: 'blue.300' }}>
                  Status:
                </Text>
                <Badge colorScheme={autoBidSettings.isActive ? 'green' : 'red'}>
                  {autoBidSettings.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </HStack>
              <HStack justify="space-between">
                <Text fontSize="sm" color="blue.700" _dark={{ color: 'blue.300' }}>
                  Max Budget:
                </Text>
                <CurrencyDisplay 
                  amount={autoBidSettings.maxBudget} 
                  baseCurrency={currency}
                  size="sm"
                />
              </HStack>
              <HStack justify="space-between">
                <Text fontSize="sm" color="blue.700" _dark={{ color: 'blue.300' }}>
                  Increment:
                </Text>
                <CurrencyDisplay 
                  amount={autoBidSettings.incrementStep} 
                  baseCurrency={currency}
                  size="sm"
                />
              </HStack>
              {autoBidSettings.currentBid && (
                <HStack justify="space-between">
                  <Text fontSize="sm" color="blue.700" _dark={{ color: 'blue.300' }}>
                    Last Auto-bid:
                  </Text>
                  <CurrencyDisplay 
                    amount={autoBidSettings.currentBid} 
                    baseCurrency={currency}
                    size="sm"
                  />
                </HStack>
              )}
            </VStack>
          </Box>
        )}

        <Divider />

        {/* Auto-bid Form */}
        <form onSubmit={handleSubmit(onSubmit)}>
          <VStack spacing={4} align="stretch">
            <FormControl isInvalid={!!errors.maxBudget}>
              <FormLabel>{t('maxBudget')}</FormLabel>
              <NumberInput
                min={minimumBid + 1}
                precision={2}
                value={maxBudget}
                onChange={(valueString) => setValue('maxBudget', parseFloat(valueString) || 0)}
              >
                <NumberInputField
                  {...register('maxBudget', {
                    required: 'Max budget is required',
                    min: {
                      value: minimumBid + 1,
                      message: `Max budget must be higher than minimum bid (${minimumBid + 1})`,
                    },
                  })}
                />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
              <FormErrorMessage>{errors.maxBudget?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.incrementStep}>
              <FormLabel>{t('incrementStep')}</FormLabel>
              <NumberInput
                min={0.01}
                max={10}
                precision={2}
                value={incrementStep}
                onChange={(valueString) => setValue('incrementStep', parseFloat(valueString) || 1)}
              >
                <NumberInputField
                  {...register('incrementStep', {
                    required: 'Increment step is required',
                    min: {
                      value: 0.01,
                      message: 'Minimum increment is 0.01',
                    },
                    max: {
                      value: 10,
                      message: 'Maximum increment is 10',
                    },
                  })}
                />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
              <FormErrorMessage>{errors.incrementStep?.message}</FormErrorMessage>
            </FormControl>

            {/* Potential Bids Preview */}
            {potentialBids.length > 0 && (
              <Box p={3} bg="gray.50" borderRadius="md" _dark={{ bg: 'gray.700' }}>
                <Text fontSize="sm" fontWeight="semibold" mb={2}>
                  Potential auto-bids:
                </Text>
                <HStack spacing={2} flexWrap="wrap">
                  {potentialBids.map((bid, index) => (
                    <Badge key={index} variant="outline" colorScheme="blue">
                      <CurrencyDisplay 
                        amount={bid} 
                        baseCurrency={currency}
                        size="sm"
                        showTooltip={false}
                      />
                    </Badge>
                  ))}
                  {potentialBids.length >= 5 && <Text fontSize="xs">...</Text>}
                </HStack>
              </Box>
            )}

            {/* Error Display */}
            {(setupAutoBidMutation.error || deactivateAutoBidMutation.error) && (
              <Alert status="error">
                <AlertIcon />
                {setupAutoBidMutation.error?.message || deactivateAutoBidMutation.error?.message}
              </Alert>
            )}

            {/* Action Buttons */}
            <HStack spacing={3}>
              <Button
                type="submit"
                colorScheme="blue"
                flex={1}
                isLoading={setupAutoBidMutation.isPending}
                loadingText="Setting up..."
                isDisabled={!maxBudget || !incrementStep || maxBudget <= minimumBid}
              >
                {autoBidSettings?.isActive ? 'Update Auto-bid' : 'Setup Auto-bid'}
              </Button>
              
              {autoBidSettings?.isActive && (
                <Button
                  variant="outline"
                  colorScheme="red"
                  onClick={handleDeactivate}
                  isLoading={deactivateAutoBidMutation.isPending}
                  loadingText="Deactivating..."
                >
                  Deactivate
                </Button>
              )}
            </HStack>
          </VStack>
        </form>

        {/* Auto-bid Info */}
        <Box p={3} bg="gray.50" borderRadius="md" _dark={{ bg: 'gray.700' }}>
          <Text fontSize="xs" color="gray.600" _dark={{ color: 'gray.400' }}>
            • Auto-bid will place bids automatically when you are outbid
            • Bids will not exceed your maximum budget
            • You can modify or deactivate auto-bid at any time
            • Auto-bid stops when auction ends or budget is reached
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};

export default AutoBidForm;
