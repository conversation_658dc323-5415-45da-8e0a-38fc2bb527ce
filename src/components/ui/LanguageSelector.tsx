'use client';

import React, { useState } from 'react';
import {
  Box,
  Button,
  Menu,
  Men<PERSON><PERSON>on,
  MenuList,
  MenuItem,
  HStack,
  Text,
  Image,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';

interface Language {
  code: string;
  name: string;
  flag: string;
}

const languages: Language[] = [
  {
    code: 'id',
    name: 'Bahasa Indonesia',
    flag: '🇮🇩',
  },
  {
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
  },
];

interface LanguageSelectorProps {
  variant?: 'button' | 'menu';
  size?: 'sm' | 'md' | 'lg';
  showFlag?: boolean;
  showText?: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'menu',
  size = 'md',
  showFlag = true,
  showText = true,
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  const handleLanguageChange = (languageCode: string) => {
    // Remove current locale from pathname
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}(?=\/|$)/, '') || '/';
    
    // Navigate to new locale
    router.push(`/${languageCode}${pathWithoutLocale}`);
  };

  if (variant === 'button') {
    return (
      <HStack spacing={2}>
        {languages.map((language) => (
          <Button
            key={language.code}
            size={size}
            variant={locale === language.code ? 'solid' : 'outline'}
            colorScheme={locale === language.code ? 'blue' : 'gray'}
            onClick={() => handleLanguageChange(language.code)}
            leftIcon={showFlag ? <Text fontSize="sm">{language.flag}</Text> : undefined}
          >
            {showText ? language.code.toUpperCase() : language.flag}
          </Button>
        ))}
      </HStack>
    );
  }

  return (
    <Menu>
      <MenuButton
        as={Button}
        size={size}
        variant="outline"
        rightIcon={<ChevronDownIcon />}
        bg={bgColor}
        borderColor={borderColor}
        _hover={{ bg: hoverBg }}
        _active={{ bg: hoverBg }}
      >
        <HStack spacing={2}>
          {showFlag && <Text fontSize="sm">{currentLanguage.flag}</Text>}
          {showText && (
            <Text fontSize="sm" fontWeight="medium">
              {currentLanguage.code.toUpperCase()}
            </Text>
          )}
        </HStack>
      </MenuButton>
      
      <MenuList bg={bgColor} borderColor={borderColor}>
        {languages.map((language) => (
          <MenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            bg={locale === language.code ? 'blue.50' : 'transparent'}
            _hover={{ bg: hoverBg }}
            _dark={{
              bg: locale === language.code ? 'blue.900' : 'transparent',
              _hover: { bg: 'gray.700' },
            }}
          >
            <HStack spacing={3} width="100%">
              <Text fontSize="lg">{language.flag}</Text>
              <Box>
                <Text fontWeight="medium" fontSize="sm">
                  {language.name}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {language.code.toUpperCase()}
                </Text>
              </Box>
              {locale === language.code && (
                <Box ml="auto">
                  <Box
                    w={2}
                    h={2}
                    bg="blue.500"
                    borderRadius="full"
                  />
                </Box>
              )}
            </HStack>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

export default LanguageSelector;
