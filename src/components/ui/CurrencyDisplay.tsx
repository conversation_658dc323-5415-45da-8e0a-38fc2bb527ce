'use client';

import React from 'react';
import {
  Box,
  Text,
  HStack,
  VStack,
  Toolt<PERSON>,
  Badge,
  useColorModeValue,
  Spinner,
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useLocale, useTranslations } from 'next-intl';

interface CurrencyDisplayProps {
  amount: number;
  baseCurrency?: 'USD' | 'IDR';
  showOriginal?: boolean;
  showTooltip?: boolean;
  size?: 'sm' | 'md' | 'lg';
  fontWeight?: string;
  color?: string;
}

interface ConvertedPrice {
  originalPrice: number;
  originalCurrency: string;
  convertedPrice: number;
  convertedCurrency: string;
  exchangeRate?: number;
  formattedPrice: string;
}

const CurrencyDisplay: React.FC<CurrencyDisplayProps> = ({
  amount,
  baseCurrency = 'USD',
  showOriginal = false,
  showTooltip = true,
  size = 'md',
  fontWeight = 'semibold',
  color,
}) => {
  const locale = useLocale();
  const t = useTranslations('Currency');
  
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Get target currency based on locale
  const targetCurrency = locale === 'id' ? 'IDR' : 'USD';

  // Fetch converted price
  const { data: convertedPrice, isLoading } = useQuery<ConvertedPrice>({
    queryKey: ['convertProductPrice', amount, baseCurrency, locale],
    queryFn: async () => {
      const response = await fetch(
        `/api/v1/currency/product-price?priceUSD=${amount}&locale=${locale}`
      );
      if (!response.ok) throw new Error('Failed to convert price');
      const result = await response.json();
      return result.data;
    },
    enabled: baseCurrency !== targetCurrency,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const getFontSize = () => {
    switch (size) {
      case 'sm':
        return 'sm';
      case 'lg':
        return 'xl';
      case 'md':
      default:
        return 'md';
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat(locale === 'id' ? 'id-ID' : 'en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'IDR' ? 0 : 2,
      maximumFractionDigits: currency === 'IDR' ? 0 : 2,
    }).format(amount);
  };

  // If same currency, just display the amount
  if (baseCurrency === targetCurrency || !convertedPrice) {
    return (
      <Text
        fontSize={getFontSize()}
        fontWeight={fontWeight}
        color={color || textColor}
      >
        {isLoading ? (
          <HStack spacing={2}>
            <Spinner size="sm" />
            <Text>Loading...</Text>
          </HStack>
        ) : (
          formatCurrency(amount, baseCurrency)
        )}
      </Text>
    );
  }

  const mainPrice = convertedPrice.formattedPrice;
  const originalPrice = formatCurrency(amount, baseCurrency);
  const exchangeRate = convertedPrice.exchangeRate;

  const PriceDisplay = () => (
    <VStack spacing={1} align="start">
      <Text
        fontSize={getFontSize()}
        fontWeight={fontWeight}
        color={color || textColor}
      >
        {mainPrice}
      </Text>
      
      {showOriginal && (
        <HStack spacing={2}>
          <Text fontSize="xs" color={mutedColor}>
            {originalPrice}
          </Text>
          {exchangeRate && (
            <Badge size="sm" colorScheme="blue" variant="subtle">
              1 {baseCurrency} = {exchangeRate.toLocaleString()} {targetCurrency}
            </Badge>
          )}
        </HStack>
      )}
    </VStack>
  );

  if (!showTooltip) {
    return <PriceDisplay />;
  }

  return (
    <Tooltip
      label={
        <VStack spacing={1} align="start">
          <Text fontSize="sm">
            {t('priceIn', { currency: targetCurrency })}: {mainPrice}
          </Text>
          <Text fontSize="sm">
            {t('priceIn', { currency: baseCurrency })}: {originalPrice}
          </Text>
          {exchangeRate && (
            <Text fontSize="xs" color="gray.300">
              {t('exchangeRate')}: 1 {baseCurrency} = {exchangeRate.toLocaleString()} {targetCurrency}
            </Text>
          )}
        </VStack>
      }
      placement="top"
      hasArrow
    >
      <Box cursor="help">
        <PriceDisplay />
      </Box>
    </Tooltip>
  );
};

export default CurrencyDisplay;
