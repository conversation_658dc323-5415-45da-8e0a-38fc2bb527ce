'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Menu,
  Menu<PERSON>utton,
  MenuList,
  MenuItem,
  HStack,
  Text,
  useColorModeValue,
  Badge,
} from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { useLocale, useTranslations } from 'next-intl';
import { useQuery } from '@tanstack/react-query';

interface Currency {
  code: string;
  name: string;
  symbol: string;
}

interface CurrencySelectorProps {
  value?: string;
  onChange?: (currency: string) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'button' | 'menu';
  showName?: boolean;
  showSymbol?: boolean;
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  value,
  onChange,
  size = 'md',
  variant = 'menu',
  showName = false,
  showSymbol = true,
}) => {
  const locale = useLocale();
  const t = useTranslations('Currency');
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  // Get default currency based on locale
  const getDefaultCurrency = () => {
    return locale === 'id' ? 'IDR' : 'USD';
  };

  const [selectedCurrency, setSelectedCurrency] = useState<string>(
    value || getDefaultCurrency()
  );

  // Fetch supported currencies
  const { data: currencies = [] } = useQuery<Currency[]>({
    queryKey: ['supportedCurrencies'],
    queryFn: async () => {
      const response = await fetch('/api/v1/currency/supported');
      if (!response.ok) throw new Error('Failed to fetch currencies');
      const result = await response.json();
      return result.data;
    },
    staleTime: 60 * 60 * 1000, // 1 hour
  });

  // Update selected currency when locale changes
  useEffect(() => {
    if (!value) {
      const defaultCurrency = getDefaultCurrency();
      setSelectedCurrency(defaultCurrency);
      onChange?.(defaultCurrency);
    }
  }, [locale, value, onChange]);

  // Update selected currency when value prop changes
  useEffect(() => {
    if (value && value !== selectedCurrency) {
      setSelectedCurrency(value);
    }
  }, [value, selectedCurrency]);

  const handleCurrencyChange = (currencyCode: string) => {
    setSelectedCurrency(currencyCode);
    onChange?.(currencyCode);
  };

  const currentCurrency = currencies.find(c => c.code === selectedCurrency) || {
    code: selectedCurrency,
    name: selectedCurrency,
    symbol: selectedCurrency === 'USD' ? '$' : 'Rp',
  };

  if (variant === 'button') {
    return (
      <HStack spacing={2}>
        {currencies.map((currency) => (
          <Button
            key={currency.code}
            size={size}
            variant={selectedCurrency === currency.code ? 'solid' : 'outline'}
            colorScheme={selectedCurrency === currency.code ? 'blue' : 'gray'}
            onClick={() => handleCurrencyChange(currency.code)}
          >
            <HStack spacing={1}>
              {showSymbol && <Text>{currency.symbol}</Text>}
              <Text>{currency.code}</Text>
              {showName && <Text fontSize="xs">({currency.name})</Text>}
            </HStack>
          </Button>
        ))}
      </HStack>
    );
  }

  return (
    <Menu>
      <MenuButton
        as={Button}
        size={size}
        variant="outline"
        rightIcon={<ChevronDownIcon />}
        bg={bgColor}
        borderColor={borderColor}
        _hover={{ bg: hoverBg }}
        _active={{ bg: hoverBg }}
      >
        <HStack spacing={2}>
          {showSymbol && (
            <Text fontWeight="bold">{currentCurrency.symbol}</Text>
          )}
          <Text fontWeight="medium">{currentCurrency.code}</Text>
          {showName && (
            <Text fontSize="xs" color="gray.500">
              ({currentCurrency.name})
            </Text>
          )}
        </HStack>
      </MenuButton>
      
      <MenuList bg={bgColor} borderColor={borderColor}>
        {currencies.map((currency) => (
          <MenuItem
            key={currency.code}
            onClick={() => handleCurrencyChange(currency.code)}
            bg={selectedCurrency === currency.code ? 'blue.50' : 'transparent'}
            _hover={{ bg: hoverBg }}
            _dark={{
              bg: selectedCurrency === currency.code ? 'blue.900' : 'transparent',
              _hover: { bg: 'gray.700' },
            }}
          >
            <HStack spacing={3} width="100%">
              <Text fontWeight="bold" fontSize="lg">
                {currency.symbol}
              </Text>
              <Box>
                <Text fontWeight="medium" fontSize="sm">
                  {currency.code}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {currency.name}
                </Text>
              </Box>
              {selectedCurrency === currency.code && (
                <Box ml="auto">
                  <Badge colorScheme="blue" size="sm">
                    Selected
                  </Badge>
                </Box>
              )}
            </HStack>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

export default CurrencySelector;
