"use client"
import React, { useState, useEffect } from 'react'
import {
    <PERSON>,
    But<PERSON>,
    Text,
    VStack,
    <PERSON><PERSON><PERSON>ck,
    <PERSON>ge,
    Flex,
    createList<PERSON>oll<PERSON>tion,
    Icon,
    Skeleton,
} from '@chakra-ui/react'
import { FaGavel, FaHeart, FaClock, FaHistory, FaRecycle, FaSyncAlt } from 'react-icons/fa'
import { formatUSD, getTimeLeftString } from '@/utils/helpers/helper'
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns'
import { usePlaceBidMutation, useUserBidQuery, useBidHistoryQuery } from '@/services/useBiddingQuery'
import ClientOnly from '@/components/ui/ClientOnly'
import { ChakraSelect } from '../ui/select/ChakraSelect'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

interface AuctionInfoProps {
    currentBid: number;
    startingPrice: number;
    bidCount: number;
    auctionStartDate: string;
    auctionEndDate: string;
    extendedBiddingEnabled?: boolean;
    extendedBiddingMinutes?: number;
    extendedBiddingDuration?: number;
    productId: string;
    productName: string;
    onPlaceBid?: (productId: string, bidAmount: number) => void;
    onAddToWishlist?: (productId: string) => void;
    isInWishlist?: boolean;
    onShowBidHistory?: (productId: string) => void;
    mb?: number;
    isLoadingAuth?: boolean;
    isAuthenticated?: boolean;
    auctionStatus?: 'active' | 'ended' | 'upcoming'; // Added auctionStatus prop
    timeLeft?: string; // Added timeLeft prop for auction status
}

const AuctionInfo: React.FC<AuctionInfoProps> = ({
    currentBid,
    startingPrice,
    bidCount,
    auctionStartDate,
    auctionEndDate,
    extendedBiddingEnabled = false,
    extendedBiddingMinutes = 5,
    extendedBiddingDuration = 10,
    productId,
    productName,
    onPlaceBid,
    onAddToWishlist,
    isInWishlist = false,
    onShowBidHistory,
    isLoadingAuth = false,
    isAuthenticated = false,
    auctionStatus = 'active', 
    timeLeft = 'N/A', 
    mb = 0
}) => {
    const router = useRouter();
    const dataSession = useSession()
    const [bidAmount, setBidAmount] = useState(currentBid + 1);

    const placeBidMutation = usePlaceBidMutation();
    const { data: userBid } = useUserBidQuery(productId, dataSession?.data?.user?.id || '');
    const { data: bidHistory } = useBidHistoryQuery(productId);

    const minBidIncrement = Math.max(1, Math.floor(currentBid * 0.05)); // 5% increment or $1 minimum
    const minBidAmount = currentBid + minBidIncrement;

    useEffect(() => {
        setBidAmount(minBidAmount);
    }, [minBidAmount]);

    const handleBidAmountChange = (value: string) => {
        const numValue = parseFloat(value) || 0;
        setBidAmount(numValue);
    };

    // Generate bid amount options from minimum to $1,000,000
    const generateBidOptions = () => {
        const options = [];
        let currentAmount = minBidAmount;

        // Add incremental options up to $1,000,000
        while (currentAmount <= 1000000) {
            options.push(currentAmount);

            if (currentAmount < 100) {
                currentAmount += 5; // $5 increments under $100
            } else if (currentAmount < 1000) {
                currentAmount += 25; // $25 increments under $1,000
            } else if (currentAmount < 10000) {
                currentAmount += 100; // $100 increments under $10,000
            } else if (currentAmount < 100000) {
                currentAmount += 500; // $500 increments under $100,000
            } else {
                currentAmount += 1000; // $1,000 increments above $100,000
            }
        }

        return options;
    };

    const bidOptions = generateBidOptions();

    const bidOptionsSelect = createListCollection({
        items: bidOptions.map(amount => ({
            label: formatUSD(amount),
            value: amount.toString()
        })),
    })

    const handlePlaceBid = async () => {
        console.log('Placing bid:', bidAmount, 'for product:', productId);
        if (bidAmount >= minBidAmount) {
            try {
                await placeBidMutation.mutateAsync({
                    productId,
                    bidAmount
                });
                // Reset bid amount to new minimum after successful bid
                setBidAmount(bidAmount + minBidIncrement);
            } catch (error) {
                console.error('Failed to place bid:', error);
            }
        }
    };

    const handleAddToWishlist = () => {
        if (onAddToWishlist) {
            onAddToWishlist(productId);
        }
    };

    const getStatusColor = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'blue';
            case 'active': return 'green';
            case 'ended': return 'gray';
            default: return 'gray';
        }
    };

    const getStatusText = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'Upcoming';
            case 'active': return 'Live Auction';
            case 'ended': return 'Auction Ended';
            default: return 'Unknown';
        }
    };

    return (
        <Box
            bg="white"
            p={6}
            borderRadius="lg"
            border="1px solid"
            borderColor="gray.200"
            mb={mb}
        >
            <VStack align="stretch" gap={4}>
                {/* Auction Status */}
                <Flex justify="space-between" align="center">
                    <Badge colorScheme={getStatusColor()} variant="solid" fontSize="sm" px={3} py={1}>
                        {getStatusText()}
                    </Badge>
                    <HStack>
                        <FaClock />
                        <ClientOnly fallback={<Text fontSize="sm" color="gray.600">Loading...</Text>}>
                            <Text fontSize="sm" color="gray.600">
                                {timeLeft}
                            </Text>
                        </ClientOnly>
                    </HStack>
                </Flex>

                {/* Current Bid Display */}
                <Box textAlign="center" py={6}>
                    <Text fontSize="sm" color="gray.600" mb={3} textTransform="uppercase" letterSpacing="wide">
                        Current Bid
                    </Text>
                    <Text fontSize="5xl" fontWeight="extrabold" color="gray.800" lineHeight="1">
                        {formatUSD(currentBid)}
                    </Text>
                    <Text fontSize="sm" color="gray.500" mt={2}>
                        {bidCount} bid{bidCount !== 1 ? 's' : ''} • Starting at {formatUSD(startingPrice)}
                    </Text>
                </Box>

                {/* User's Bid Status */}
                {userBid && (
                    <Box p={3} bg={userBid.isWinning ? "green.50" : "orange.50"} borderRadius="md">
                        <Text fontSize="sm" color={userBid.isWinning ? "green.700" : "orange.700"}>
                            {userBid.isWinning ? "🎉 You're the highest bidder!" : "⚠️ You've been outbid"}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                            Your highest bid: <Box as="span" fontWeight="bold" color="gray.800">{formatUSD(userBid.amount)}</Box>
                        </Text>
                    </Box>
                )}

                {/* Extended Bidding Info */}
                {extendedBiddingEnabled && auctionStatus === 'active' && (
                    <Box p={3} bg="blue.50" borderRadius="md">
                        <Text fontSize="xs" color="blue.700">
                            <Icon as={FaSyncAlt} mr="1" /> Extended Bidding: Auction extends {extendedBiddingDuration} minutes if bid placed in last {extendedBiddingMinutes} minutes
                        </Text>
                    </Box>
                )}

                {/* Bid Placement */}
                {auctionStatus === 'active' && (
                    <Box>
                        <Text fontSize="sm" color="gray.600" mb={2}>
                            Place Your Bid (Minimum: <Box as="span" fontWeight="bold" color="gray.800">{formatUSD(minBidAmount)}</Box>)
                        </Text>
                        {
                            isLoadingAuth ? (
                                <Skeleton
                                    width="full"
                                    height="30px"
                                    borderRadius="lg" />

                            ) :
                                isAuthenticated ? (
                                    <HStack gap="4">
                                        <ChakraSelect
                                            width='60%'
                                            size='lg'
                                            placeholder={`Bid Now`}
                                            collection={bidOptionsSelect}
                                            lazyMount={true}
                                            onValueChange={(value) => {
                                                console.log('Selected bid amount:', value);
                                                setBidAmount(value.value?.[0] ? parseFloat(value.value[0]) : 0);
                                            }}
                                        />
                                        <Button
                                            size={'lg'}
                                            colorScheme="blue"
                                            onClick={handlePlaceBid}
                                            loading={placeBidMutation.isPending}
                                            disabled={placeBidMutation.isPending || bidAmount < minBidAmount}
                                            w="40%"
                                            borderRadius={'full'}
                                        >
                                            Bid Now
                                        </Button>
                                    </HStack>
                                ) : (
                                    <Button
                                        size={'lg'}
                                        colorScheme="blue"
                                        w="full"
                                        borderRadius={'full'}
                                        onClick={() => router.push('/auth/login')}
                                    >
                                        Login to Buy / Bid
                                    </Button>

                                )
                        }

                        {bidAmount < minBidAmount && (
                            <Text fontSize="xs" color="red.500" mt={1}>
                                Bid must be at least {formatUSD(minBidAmount)}
                            </Text>
                        )}
                    </Box>
                )}

                {/* Auction Ended Message */}
                {auctionStatus === 'ended' && (
                    <Box p={4} bg="gray.100" borderRadius="md" textAlign="center">
                        <Text fontWeight="extrabold" color="gray.800">
                            Auction has ended
                        </Text>
                        {
                            bidHistory && bidHistory.length > 0 ? (
                                <Text fontSize="md" color="gray.700" fontWeight="semibold" >
                                    Final bid: <Box as="span" me={2} fontSize={24} fontWeight="bold" color="green.600">{formatUSD(currentBid)}</Box>
                                </Text>
                            ) : (
                                <Text fontSize="md" color="red.600">
                                    No bids were placed
                                </Text>
                            )
                        }
                        {userBid?.isWinning && (
                            <Text fontSize="sm" color="green.600" fontWeight="bold" mt={2}>
                                🎉 Congratulations! You won this auction!
                            </Text>
                        )}
                    </Box>
                )}

                {auctionStatus === 'upcoming' && (
                    <Box p={4} bg="blue.50" borderRadius="md" textAlign="center">
                        <Text fontWeight="bold" color="blue.700">
                            Auction hasn't started yet
                        </Text>
                        <Text fontSize="sm" color="gray.800">
                            Starting bid: {formatUSD(startingPrice)}
                        </Text>
                    </Box>
                )}

                <HStack gap={2}>
                    {/* <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleAddToWishlist}
                        color={isInWishlist ? "red.500" : "gray.600"}
                        _hover={{
                            color: isInWishlist ? "red.600" : "red.500",
                            bg: isInWishlist ? "red.50" : "gray.50"
                        }}
                        flex={1}
                    >
                        <FaHeart />
                        {isInWishlist ? 'Remove from Watchlist' : 'Add to Watchlist'}
                    </Button> */}

                    {bidHistory && bidHistory.length > 0 && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onShowBidHistory?.(productId)}
                            color="gray.680"
                            _hover={{
                                color: "blue.700",
                                bg: "blue.50"
                            }}
                        >
                            <FaHistory />
                            Bid History ({bidHistory.length})
                        </Button>
                    )}
                </HStack>

                {/* Auction Info */}
                {/* <Box pt={4} borderTop="1px solid" borderColor="gray.100">
                    <Text fontSize="xs" color="gray.500">
                        • Bid increments: {formatUSD(minBidIncrement)} minimum
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Winner pays within 48 hours
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Secure payment processing
                    </Text>
                </Box> */}
            </VStack>
        </Box>
    );
};

export default AuctionInfo;
