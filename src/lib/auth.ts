import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  accessToken: string;
  refreshToken: string;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    user: User;
    expiresAt: number;
  };
}

interface RefreshTokenResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    expiresAt: number;
  };
}

async function fetchUserProfile(accessToken: string) {
  try {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/auth/profile`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });

    if (response.data.status) {
      return response.data.data;
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    return null;
  }
}

async function refreshAccessToken(refreshToken: string) {
  try {
    const response = await axios.post<RefreshTokenResponse>(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh-token`,
      { refreshToken },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 seconds timeout
      }
    );

    if (response.data.status && response.data.data) {
      return {
        accessToken: response.data.data.accessToken,
        refreshToken: response.data.data.refreshToken,
        expiresAt: response.data.data.expiresAt,
      };
    }

    throw new Error(response.data.message || 'Failed to refresh token');
  } catch (error) {
    console.error('Token refresh failed:', error);
    throw error;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              email: credentials.email,
              password: credentials.password,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          return {
            id: data.data.user.id,
            name: `${data.data.user.firstName} ${data.data.user.lastName}`,
            email: data.data.user.email,
            firstName: data.data.user.firstName,
            lastName: data.data.user.lastName,
            phoneNumber: data.data.user.phoneNumber,
            accessToken: data.data.accessToken,
            refreshToken: data.data.refreshToken,
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 5 * 24 * 60 * 60, // 5 days
    updateAge: 24 * 60 * 60, // Update session every 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ token, user, account, trigger }) {
      // Initial sign in
      if (user && account) {
        if (account.provider === "google") {
          try {
            const { data } = await axios.post<APIResponse>(
              `${process.env.NEXT_PUBLIC_API_URL}/auth/google`,
              {
                googleToken: account.id_token,
              }
            );

            if (data.status) {
              return {
                ...token,
                id: data.data.user.id,
                firstName: data.data.user.firstName,
                lastName: data.data.user.lastName,
                phoneNumber: data.data.user.phoneNumber,
                accessToken: data.data.accessToken,
                refreshToken: data.data.refreshToken,
                expiresAt: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5 days
              };
            }
          } catch (error) {
            console.error("Google auth failed:", error);
            return token;
          }
        } else {
          // Credentials provider
          return {
            ...token,
            id: (user as any).id,
            firstName: (user as any).firstName,
            lastName: (user as any).lastName,
            phoneNumber: (user as any).phoneNumber,
            accessToken: (user as any).accessToken,
            refreshToken: (user as any).refreshToken,
            expiresAt: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5 days
          };
        }
      }

      // Refresh token if needed (5 minutes before expiry)
      if (trigger === "update" || (token.expiresAt && Date.now() > (token.expiresAt as number) - 5 * 60 * 1000)) {
        if (token.refreshToken) {
          try {
            const refreshedTokens = await refreshAccessToken(token.refreshToken as string);

            return {
              ...token,
              accessToken: refreshedTokens.accessToken,
              refreshToken: refreshedTokens.refreshToken,
              expiresAt: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5 days
              error: undefined, // Clear any previous errors
            };
          } catch (error) {
            console.error("Token refresh failed:", error);

            // Mark token as expired and return with error
            return {
              ...token,
              error: "RefreshAccessTokenError",
              expiresAt: Date.now() - 1000, // Mark as expired
            };
          }
        } else {
          // No refresh token available
          return {
            ...token,
            error: "NoRefreshToken",
            expiresAt: Date.now() - 1000, // Mark as expired
          };
        }
      }

      return token;
    },
    async session({ session, token }) {
      console.log("Session callback triggered", { session, token }, token.accessToken, token.refreshToken);
      // Handle token errors (expired, invalid, etc.)
      if (token.error) {
        console.error("Session error:", token.error);
        // Clear session user data to force re-authentication
        session.user = {} as any;
        return session;
      }

      if (token.accessToken && !token.error) {
        // Always fetch fresh profile data from API for better session management
        try {
          const profile = await fetchUserProfile(token.accessToken as string);
          if (profile) {
            session.user = {
              ...session.user,
              id: profile.id,
              firstName: profile.firstName,
              lastName: profile.lastName,
              email: profile.email,
              phoneNumber: profile.phoneNumber,
              name: `${profile.firstName} ${profile.lastName}`,
            };
          } else {
            // Fallback to token data if profile fetch fails
            session.user = {
              ...session.user,
              id: token.id as string,
              firstName: token.firstName as string,
              lastName: token.lastName as string,
              phoneNumber: token.phoneNumber as string,
              name: `${token.firstName} ${token.lastName}`,
            };
          }
        } catch (error) {
          console.error("Failed to fetch profile:", error);
          // If profile fetch fails with 401, token might be invalid
          if (axios.isAxiosError(error) && error.response?.status === 401) {
            session.user = {} as any; // Clear user data to force re-authentication
            return session;
          }

          // For other errors, fallback to token data
          session.user = {
            ...session.user,
            id: token.id as string,
            firstName: token.firstName as string,
            lastName: token.lastName as string,
            phoneNumber: token.phoneNumber as string,
            name: `${token.firstName} ${token.lastName}`,
          };
        }

        // Attach tokens to session for API calls
        (session as any).accessToken = token.accessToken;
        (session as any).refreshToken = token.refreshToken;
        (session as any).expiresAt = token.expiresAt;
      } else {
        // No valid token, clear user data to force re-authentication
        session.user = {} as any;
      }

      return session;
    },
  },
};