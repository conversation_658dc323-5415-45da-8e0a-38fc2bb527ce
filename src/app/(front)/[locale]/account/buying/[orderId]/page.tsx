'use client'
import React from 'react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Grid,
    Skeleton,
    Flex,
    Icon,
    Container,
    Separator,
    GridItem,
} from '@chakra-ui/react'
import {
    FaArrowLeft,
    FaShoppingBag,
    FaClock,
    FaDollarSign,
    FaTruck,
    FaCheckCircle,
    FaTimesCircle,
    FaSpinner,
    FaMapMarkerAlt,
    FaCreditCard
} from 'react-icons/fa'
import { useOrderQuery } from '@/services/useOrderQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow, format } from 'date-fns'
import { useRouter, useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'orange'
            case 'processing': return 'blue'
            case 'shipped': return 'purple'
            case 'delivered': return 'green'
            case 'cancelled': return 'red'
            default: return 'gray'
        }
    }

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending': return FaClock
            case 'processing': return FaSpinner
            case 'shipped': return FaTruck
            case 'delivered': return FaCheckCircle
            case 'cancelled': return FaTimesCircle
            default: return FaClock
        }
    }

    return (
        <Badge colorScheme={getStatusColor(status)} variant="subtle" size="lg">
            <HStack gap={2}>
                <Icon as={getStatusIcon(status)} boxSize={4} />
                <Text textTransform="capitalize" fontWeight="medium">{status}</Text>
            </HStack>
        </Badge>
    )
}

// Payment status badge component
const PaymentStatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'orange'
            case 'paid': return 'green'
            case 'failed': return 'red'
            case 'refunded': return 'blue'
            default: return 'gray'
        }
    }

    return (
        <Badge colorScheme={getStatusColor(status)} variant="outline">
            <Text textTransform="capitalize" fontWeight="medium">{status}</Text>
        </Badge>
    )
}

const OrderDetailPage = () => {
    const router = useRouter()
    const params = useParams()
    const t = useTranslations()
    const { data: session } = useSession()
    const orderId = params.orderId as string

    const { data: order, isLoading, error } = useOrderQuery(orderId)

    if (!session) {
        return (
            <Container maxW="6xl" py={8}>
                <Text>Please login to view order details.</Text>
            </Container>
        )
    }

    if (isLoading) {
        return (
            <Container maxW="6xl" py={8}>
                <VStack gap={6} align="stretch">
                    <Skeleton height="60px" />
                    <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
                        <VStack gap={6} align="stretch">
                            <Skeleton height="300px" />
                            <Skeleton height="200px" />
                        </VStack>
                        <VStack gap={6} align="stretch">
                            <Skeleton height="200px" />
                            <Skeleton height="150px" />
                        </VStack>
                    </Grid>
                </VStack>
            </Container>
        )
    }

    if (error || !order) {
        return (
            <Container maxW="6xl" py={8}>
                <Box p={8} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                    <VStack gap={4}>
                        <Icon as={FaTimesCircle} boxSize={12} color="red.500" />
                        <Text color="red.500">Order not found or failed to load</Text>
                        <Button colorScheme="blue" onClick={() => router.push('/account/buying')}>
                            Back to Orders
                        </Button>
                    </VStack>
                </Box>
            </Container>
        )
    }

    return (
        <Container maxW="6xl" py={8}>
            {/* Header */}
            <VStack align="start" gap={6} mb={8}>
                <HStack alignItems="center" gap={4}>
                    <Link href='/account/buying'>
                        <Icon as={FaArrowLeft} boxSize={4} />
                    </Link>
                    <Text as="div" fontSize="sm" color="gray.800">
                        Back to Orders
                    </Text>
                </HStack>

                <VStack align="start" gap={2} w="full">
                    <Heading size="xl">Order #{order.orderNumber}</Heading>
                    <HStack gap={4} wrap="wrap">
                        <Text color="gray.600">
                            Placed {formatDistanceToNow(new Date(order.createdAt), { addSuffix: true })}
                        </Text>
                        <StatusBadge status={order.status} />
                        <PaymentStatusBadge status={order.paymentStatus} />
                    </HStack>
                </VStack>
            </VStack>

            <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
                {/* Left Column - Order Items */}
                <VStack gap={6} align="stretch">
                    {/* Order Items */}
                    <Box p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                        <VStack gap={4} align="stretch">
                            <Heading size="md">Order Items</Heading>
                            <Separator />
                            {order.items.map((item) => (
                                <HStack key={item.id} gap={4} align="start">
                                    <Image
                                        src={item.product.images.find(img => img.isMain)?.imageUrl || '/placeholder.jpg'}
                                        alt={item.product.itemName}
                                        boxSize="80px"
                                        objectFit="cover"
                                        borderRadius="md"
                                    />
                                    <VStack align="start" flex={1} gap={2}>
                                        <Text fontWeight="medium" fontSize="lg">
                                            {item.product.itemName}
                                        </Text>
                                        <HStack gap={4}>
                                            <Text fontSize="sm" color="gray.600">
                                                Quantity: {item.quantity}
                                            </Text>
                                            <Text fontSize="sm" color="gray.600">
                                                Unit Price: {formatUSD(item.price)}
                                            </Text>
                                        </HStack>
                                        <Text fontWeight="medium" color="gray.800">
                                            Subtotal: {formatUSD(item.price * item.quantity)}
                                        </Text>
                                        {item.product.slug && (
                                            <Link href={`/${item.product.sellType}/${item.product.slug}`}>
                                                <Button size="sm" variant="outline">
                                                    View Product
                                                </Button>
                                            </Link>
                                        )}
                                    </VStack>
                                </HStack>
                            ))}
                        </VStack>
                    </Box>

                    {/* Shipping Address */}
                    {order.shippingAddress && (
                        <Box p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                            <VStack gap={4} align="stretch">
                                <HStack gap={2}>
                                    <Icon as={FaMapMarkerAlt} color="blue.500" />
                                    <Heading size="md">Shipping Address</Heading>
                                </HStack>
                                <Separator />
                                <VStack align="start" gap={1}>
                                    <Text fontWeight="medium">{order.shippingAddress.name}</Text>
                                    <Text color="gray.600">{order.shippingAddress.address}</Text>
                                    <Text color="gray.600">
                                        {order.shippingAddress.city}, {order.shippingAddress.provinceRegion} {order.shippingAddress.zipCode}
                                    </Text>
                                    <Text color="gray.600">{order.shippingAddress.country}</Text>
                                </VStack>
                            </VStack>
                        </Box>
                    )}
                </VStack>

                {/* Right Column - Order Summary & Payment */}
                <VStack gap={6} align="stretch">
                    {/* Order Summary */}
                    <Box p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                        <VStack gap={4} align="stretch">
                            <Heading size="md">Order Summary</Heading>
                            <Separator />
                            <VStack gap={3} align="stretch">
                                <Flex justify="space-between">
                                    <Text>Subtotal</Text>
                                    <Text>{formatUSD(order.subtotal)}</Text>
                                </Flex>
                                <Flex justify="space-between">
                                    <Text>Shipping</Text>
                                    <Text>{formatUSD(order.shippingCost)}</Text>
                                </Flex>
                                <Flex justify="space-between">
                                    <Text>Tax</Text>
                                    <Text>{formatUSD(order.tax)}</Text>
                                </Flex>
                                <Separator />
                                <Flex justify="space-between">
                                    <Text fontWeight="bold" fontSize="lg">Total</Text>
                                    <Text fontWeight="bold" fontSize="lg" color="gray.800">
                                        {formatUSD(order.total)}
                                    </Text>
                                </Flex>
                            </VStack>
                        </VStack>
                    </Box>

                    {/* Payment Information */}
                    <Box p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                        <VStack gap={4} align="stretch">
                            <HStack gap={2}>
                                <Icon as={FaCreditCard} color="blue.500" />
                                <Heading size="md">Payment Information</Heading>
                            </HStack>
                            <Separator />
                            <VStack align="start" gap={2}>
                                <HStack justify="space-between" w="full">
                                    <Text color="gray.600">Payment Method</Text>
                                    <Text textTransform="capitalize">
                                        {order.paymentMethod?.replace('_', ' ') || 'Not specified'}
                                    </Text>
                                </HStack>
                                <HStack justify="space-between" w="full">
                                    <Text color="gray.600">Payment Status</Text>
                                    <PaymentStatusBadge status={order.paymentStatus} />
                                </HStack>
                                <HStack justify="space-between" w="full">
                                    <Text color="gray.600">Order Date</Text>
                                    <Text>{format(new Date(order.createdAt), 'MMM dd, yyyy')}</Text>
                                </HStack>
                            </VStack>
                        </VStack>
                    </Box>

                    {/* Order Notes */}
                    {order.notes && (
                        <Box p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                            <VStack gap={4} align="stretch">
                                <Heading size="md">Order Notes</Heading>
                                <Separator />
                                <Text color="gray.600">{order.notes}</Text>
                            </VStack>
                        </Box>
                    )}
                </VStack>
            </Grid>
        </Container>
    )
}

export default OrderDetailPage
